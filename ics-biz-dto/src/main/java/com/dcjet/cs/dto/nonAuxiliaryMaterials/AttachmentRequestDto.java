package com.dcjet.cs.dto.nonAuxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 附件接口请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@ApiModel(value = "附件接口请求参数")
@Setter
@Getter
public class AttachmentRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 进货单号
     */
    @ApiModelProperty(value = "进货单号", required = true, example = "PO202312001")
    @NotBlank(message = "进货单号不能为空")
    private String purchaseOrderNo;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表", required = true)
    @NotEmpty(message = "附件列表不能为空")
    @Valid
    private List<AttachmentItemDto> attachmentList;
}
