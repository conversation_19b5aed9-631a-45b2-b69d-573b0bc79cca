package com.dcjet.cs.dto.nonAuxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 附件项DTO
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@ApiModel(value = "附件项信息")
@Setter
@Getter
public class AttachmentItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型", required = true, example = "invoiceNo")
    @NotBlank(message = "附件类型不能为空")
    private String attachmentType;

    /**
     * 附件编号
     */
    @ApiModelProperty(value = "附件编号", required = true, example = "INV001")
    @NotBlank(message = "附件编号不能为空")
    private String attachmentNo;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true, example = "invoice.pdf")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /**
     * 文件内容(base64编码)
     */
    @ApiModelProperty(value = "文件内容(base64编码)", required = true)
    @NotBlank(message = "文件内容不能为空")
    private String fileContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "发票附件")
    private String note;
}
