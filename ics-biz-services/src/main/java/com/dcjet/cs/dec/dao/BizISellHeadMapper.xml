<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizISellHeadMapper">
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.purchase_order_number,
        t.purchasing_unit,
        t.selling_unit,
        t.tax_rate,
        t.date_of_sale,
        t.remark,
        t.sales_document_status,
        t.sales_data_confirmation_time,
        t.send_ufida,
        t.drawer,
        t.business_date,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code,
        t.CONTRACT_NO,
        t.customer,
        t.SEND_FINANCIAL,
        t.IS_FLUSH_RED
    </sql>

    <sql id="condition">

    </sql>
    <insert id="insertHeadList">
        insert into T_BIZ_I_SELL_HEAD(
                                      SID,
                                      HEAD_ID,
                                      PURCHASE_ORDER_NUMBER,
                                      PURCHASING_UNIT,
                                      SELLING_UNIT,
                                      date_of_sale,
                                      TAX_RATE,
                                      SALES_DOCUMENT_STATUS,
                                      SEND_UFIDA,
                                      DRAWER,
                                      BUSINESS_DATE,
                                      INSERT_USER,
                                      INSERT_USER_NAME,
                                      INSERT_TIME,
                                      TRADE_CODE)
        select
            #{sellSid},
            #{sid},
            h.order_no,
            wh.LADING_DEPARTMENT,
            '中国烟草上海进出口有限责任公司',
            now(),
            (select mi.TAX_RATE
             from T_BIZ_WAREHOUSE_RECEIPT_LIST wl
                      left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = wl.GOODS_NAME and mi.DATA_STATE = '0'
             where wl.parent_Id = wh.sid
            limit 1),
            '0',
            '0',
            #{userInfo.userName},
            now(),
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
            from t_biz_i_order_head h
            left join T_BIZ_WAREHOUSE_RECEIPT_HEAD wh on wh.PARENT_ID = #{sid}
        where h.sid = #{sid};
        insert into T_BIZ_I_SELL_LIST(
                                      SID,
                                      HEAD_ID,
                                      TRADE_NAME,
                                      UNIT,
                                      QUANTITY,
                                      UNIT_PRICE_EXCLUDING_TAX,
                                      AMOUNT_OF_TAX,
                                      TAX_NOT_INCLUDED,
                                      TOTAL_VALUE_TAX,
                                      INSERT_USER,
                                      INSERT_USER_NAME,
                                      INSERT_TIME,
                                      TRADE_CODE)
        select
            sys_guid(),
            #{sellSid},
            lb.PRODUCT_GRADE,
            lb.UNIT,
            lb.quantity,
            mi.PRICE_EXCLUDING_TAX,
            lb.quantity*(COALESCE(mi.TAX_RATE, 0)/100),
            lb.quantity*COALESCE(mi.PRICE_EXCLUDING_TAX, 0),
            lb.quantity * (COALESCE(mi.PRICE_EXCLUDING_TAX, 0) + COALESCE(mi.TAX_RATE, 0)),
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from T_BIZ_I_PURCHASE_LIST_BOX lb
        left join T_BIZ_I_PURCHASE_HEAD ph on lb.HEAD_ID = ph.sid
        left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = lb.PRODUCT_GRADE  and mi.DATA_STATE = '0'
        where ph.HEAD_ID = #{sid};
    </insert>

    <!--
         insert into T_BIZ_I_SELL_LIST(
            SID,
            HEAD_ID,
            TRADE_NAME,
            UNIT,
            QUANTITY,
            I_COUNT,
            I_UNIT,
            unit_price_excluding_tax,
            amount_of_tax,
            tax_not_included,
            total_value_tax,
            INSERT_USER,
            INSERT_USER_NAME,
            INSERT_TIME,
            TRADE_CODE)
        select
            sys_guid(),
            #{sellSid},
            lb.goods_name,
            lb.UNIT,
            lb.quantity,
            lb.in_Quantity,
            lb.in_unit,
            (select tb.price_excluding_tax from t_biz_quotation tb where tb.g_name = lb.goods_name and tb.specifications = lb.product_model and tb.status = '0' limit 1),
            (select tb.price_excluding_tax from t_biz_quotation tb where tb.g_name = lb.goods_name and tb.specifications = lb.product_model and tb.status = '0' limit 1)*COALESCE(mi.TAX_RATE, 0),
            lb.quantity*COALESCE((select tb.price_excluding_tax from t_biz_quotation tb where tb.g_name = lb.goods_name and tb.specifications = lb.product_model and tb.status = '0' limit 1), 0),
            lb.quantity * (COALESCE((select tb.price_excluding_tax from t_biz_quotation tb where tb.g_name = lb.goods_name and tb.specifications = lb.product_model and tb.status = '0' limit 1), 0) + COALESCE(mi.TAX_RATE, 0)),
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from T_BIZ_INCOMING_GOODS_LIST lb
            left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = lb.goods_name and mi.DATA_STATE = '0'
        where lb.HEAD_ID = #{sid};

    -->
    <insert id="insertIncomingHeadList">
        insert into T_BIZ_I_SELL_HEAD(
            SID,
            HEAD_ID,
            business_date,
            PURCHASE_ORDER_NUMBER,
            CONTRACT_NO,
            customer,
            SELLING_UNIT,
            TAX_RATE,
            SALES_DOCUMENT_STATUS,
            SEND_FINANCIAL,
            IS_FLUSH_RED,
            DRAWER,
            INSERT_USER,
            INSERT_USER_NAME,
            INSERT_TIME,
            TRADE_CODE)
        select
            #{sellSid},
            #{sid},
            now(),
            h.PURCHASE_NO,
            h.CONTRACT_NO,
            '0004',
            '中国烟草上海进出口有限责任公司',
            (select mi.TAX_RATE
             from T_BIZ_INCOMING_GOODS_LIST wl
                      left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = wl.GOODS_NAME and mi.DATA_STATE = '0'
             where wl.head_id = h.id
                limit 1),
            '0',
            '0',
            '1',
            #{userInfo.userName},
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from T_BIZ_INCOMING_GOODS_HEAD h
        where h.id = #{sid};

    </insert>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizISellHead" parameterType="com.dcjet.cs.dec.model.BizISellHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_HEAD t
        <where>
            t.trade_code = #{tradeCode}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizISellHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_HEAD t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="checkIsExtract" resultType="java.lang.Integer">
        select count(1) from t_biz_store_i_head s where  s.STATUS != '2' and s.TRADE_CODE = #{tradeCode} and s.store_i_no = #{purchaseOrderNo}
    </select>
</mapper>