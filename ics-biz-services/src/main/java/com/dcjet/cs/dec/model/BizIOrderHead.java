package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import org.checkerframework.checker.units.qual.A;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.util.List;


/**
 * 进口管理-订单信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Getter
@Setter
@Table(name = "t_biz_i_order_head")
public class BizIOrderHead implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主建sid
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 订单制单时间
     * timestamp
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 订单制单时间-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 订单数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "order_data_status")
    private String orderDataStatus;

    /**
     * 合同编号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 订单编号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 甲方
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "party_a")
    private String partyA;

    /**
     * 乙方
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "party_b")
    private String partyB;

    /**
     * 交货日期
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "delivery_date")
    private String deliveryDate;

    /**
     * 付款方式
     * 字符类型(30)
     * 非必填
     */
    @Column(name = "payment_method")
    private String paymentMethod;

    /**
     * 进货单号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "purchase_order_no")
    private String purchaseOrderNo;

    /**
     * 进口发票号码
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "import_invoice_no")
    private String importInvoiceNo;

    /**
     * 许可证号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "license_no")
    private String licenseNo;

    /**
     * 准运证编号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "transport_permit_no")
    private String transportPermitNo;

    /**
     * 销售发票号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "sales_invoice_no")
    private String salesInvoiceNo;

    /**
     * 销售合同号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "sales_contract_no")
    private String salesContractNo;

    /**
     * 进货数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "purchase_data_status")
    private String purchaseDataStatus;

    /**
     * 销售数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "sales_data_status")
    private String salesDataStatus;

    /**
     * 入库回单状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "inbound_receipt_status")
    private String inboundReceiptStatus;

    /**
     * 出库回单状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "outbound_receipt_status")
    private String outboundReceiptStatus;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 拓展字段2 用于记录flowInstanceId
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;


    /**
     * 签订日期
     * timestamp
     * 非必填
     */
    @Column(name = "date_of_signing")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateOfSigning;

    /**
     * 签订日期-开始时间
     */
    @Transient
    private String dateOfSigningFrom;

    /**
     * 签订日期-结束时间
     */
    @Transient
    private String dateOfSigningTo;

    /**
     * 计划编号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "plan_no")
    private String planNo;

    /**
     * 订单确认时间
     * timestamp
     * 非必填
     */
    @Column(name = "order_confirmation_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderConfirmationTime;

    /**
     * 订单确认时间-开始时间
     */
    @Transient
    private String orderConfirmationTimeFrom;

    /**
     * 订单确认时间-结束时间
     */
    @Transient
    private String orderConfirmationTimeTo;

    /**
     * 审批状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "appr_status")
    private String apprStatus;

    /**
     * 备注
     * 数据库字段：note
     * 字符类型(200)
     */
    @Column(name = "note")
    private String note;



    /**
     * 序号
     * 数据库字段：SERIAL_NO
     * 整数类型(19,0)
     */
    @Column(name = "serial_no")
    @ApiModelProperty("备注")
    private BigDecimal serialNo;


    /**
     * 进口合同表头SID
     * 数据库字段：HEAD_ID
     * 字符类型(50)
     */
    @Column(name = "head_id")
    @ApiModelProperty("进口合同表头SID")
    private String headId;


    /**
     * 是否流入下一节点
     * 数据库字段：IS_NEXT
     * 字符类型(10)
     */
    @Column(name = "is_next")
    @ApiModelProperty("是否流入下一节点")
    private String isNext;


    /**
     * 进口合同号 前缀信息
     */
    @ApiModelProperty("进口合同号-前缀信息")
    @Column(name = "contract_prefix")
    private String contractPrefix;


    /**
     * 表头是否删除
     * IS_DELETE
     * 字符类型(10)
     */
    @ApiModelProperty("是否删除")
    @Column(name = "is_delete")
    private String isDelete;

    @Transient
    private List<String> ids;
}