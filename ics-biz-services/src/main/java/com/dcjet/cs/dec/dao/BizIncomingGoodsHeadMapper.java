package com.dcjet.cs.dec.dao;


import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizIncomingGoodsHeadList;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.dto.dec.BizListExtractContractList;
import com.dcjet.cs.dto.dec.GenerateTBFileData;
import com.dcjet.cs.params.model.PriceTerms;
import com.xdo.common.token.UserInfoToken;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * 进货管理-表头数据Mapper
 */
public interface BizIncomingGoodsHeadMapper extends Mapper<BizIncomingGoodsHead>{

    /**
     * 查询获取数据
     * @param tBizIncomingGoodsHead
     * @return
     */
    List<BizIncomingGoodsHead> getList(BizIncomingGoodsHead tBizIncomingGoodsHead);
    List<BizIncomingGoodsHead> getListToStore(BizIncomingGoodsHead tBizIncomingGoodsHead);
    List<BizIncomingGoodsHead> getListBySids(List<String> sids);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);



    /**
     * 进货管理-获取供应商列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getOrderSupplierList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取港口列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPortList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取币别列表信息
     * @param
     * @return 返回结果
     */
    List<Map<String, String>> getCurrList(BizIncomingGoodsHeadParam params);


    /**
     * 进货管理-获取价格条款
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPriceTermList(@Param("tradeCode") String tradeCode);

    String getPriceTermsByCode(@Param("tradeCode") String tradeCode,@Param("code") String code);

    /** 获取单位列表
     * @param params 请求参数
     * @return 返回结果
     */
    List<Map<String, String>> getUnitList(BizIncomingGoodsHeadParam params);

    List<BizIAuxmatForContractHead> getExtractContractInfo(@Param("contractNo")String contractNo,@Param("tradeCode") String tradeCode);

    int getAuxmatForContractNumber(@Param("id")String id,@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    List<BizListExtractContractList> getContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);
    List<BizListExtractContractList> getContractList2(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);
    BigDecimal getCount(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);

    Integer getCurrentContractNoMaxSerial(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


    BigDecimal getAuxmatCurrentContractNoSum(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);
    BigDecimal getIncomingGoodsHeadSum(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);

    List<BizIncomingGoodsHeadList> getListPagedNew(BizIncomingGoodsHead tBizIncomingGoodsHead);


    int deleteDocumentByHeadId(@Param("sid") String sid);


    List<Map<String,String>> getOrderSupplierListDistinct(@Param("tradeCode") String tradeCode);

    String getPriceTerm(@Param("id") String id);

    Integer selectByPurchaseNo(@Param("purchaseNo") String purchaseNo, @Param("id") String id, @Param("tradeCode") String tradeCode);

    Integer deleteSellListByHeadId(@Param("sid") String sid);

    Integer deleteSellHeadByHeadId(@Param("sid") String sid);

    int cancelData(@Param("id") String id);

    int returnOrder(@Param("id") String id);

    Integer checkIsGenerate(@Param("id") String id);

    Integer redFlush(@Param("id") String id);

    String getCigarettePaper(@Param("id") String id);

    GenerateTBFileData getSumTotal(@Param("id") String id);

    List<BizIAuxmatForContractHead> getExtractAuxmatContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    BigDecimal getIncomingCountByContract(@Param("contractNo") String contractNoTemp, @Param("tradeCode") String tradeCode);

    String getPortByCode(@Param("portOfDeparture") String portOfDeparture, @Param("tradeCode") String tradeCode);

    int deleteSellHeadAndListByHeadId(@Param("headId") String id);
}