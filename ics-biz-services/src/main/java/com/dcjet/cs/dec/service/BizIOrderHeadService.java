package com.dcjet.cs.dec.service;

import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dec.dao.*;
import com.dcjet.cs.dec.mapper.BizIOrderHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIOrderListDtoMapper;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.dec.*;

import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.dcjet.cs.params.dao.StorehouseMapper;
import com.dcjet.cs.params.model.Storehouse;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.TaskSequencerUtils;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import dm.jdbc.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIOrderHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIOrderHeadService extends BaseService<BizIOrderHead> implements ApprovalFlowService {

    private static final Logger log = LoggerFactory.getLogger(BizIOrderHeadService.class);
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;
    @Resource
    private ThirdPartyDbService thirdPartyDbService;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizIncomingGoodsHeadMapper tBizIncomingGoodsHeadMapper;
    @Resource
    private BizIWarehouseReceiptHeadMapper bizIWarehouseReceiptHeadMapper;
    @Resource
    private BizIWarehouseReceiptListMapper bizIWarehouseReceiptListMapper;
    @Resource
    private BizIOrderHeadDtoMapper bizIOrderHeadDtoMapper;
    @Resource
    private StorehouseMapper storehouseMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;

    @Resource
    private BizISellHeadMapper bizISellHeadMapper;
    @Resource
    private BizISellListMapper bizISellListMapper;

    @Resource
    private BizIReceiptHeadMapper bizIReceiptHeadMapper;
    @Resource
    private BizIReceiptListMapper bizIReceiptListMapper;

    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;

    @Override
    public Mapper<BizIOrderHead> getMapper() {
        return bizIOrderHeadMapper;
    }
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;

    @Resource
    private BizIPurchaseListMapper bizIPurchaseListMapper;

    @Resource
    private BizIPurchaseListBoxMapper bizIPurchaseListBoxMapper;

    @Resource
    private BizIOrderListMapper bizIOrderListMapper;

    @Resource
    private GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @Resource
    private AttachedService attachedService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private CommonService commonService;

    @Resource
    private BizIOrderListDtoMapper bizIOrderListDtoMapper;


    /**
     * 获取分页信息
     *
     * @param bizIOrderHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIOrderHeadDto>> getListPaged(BizIOrderHeadParam bizIOrderHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIOrderHead bizIOrderHead = bizIOrderHeadDtoMapper.toPo(bizIOrderHeadParam);
        bizIOrderHead.setTradeCode(userInfo.getCompany());
        Page<BizIOrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIOrderHeadMapper.getList( bizIOrderHead));
        // 将PO转为DTO返回给前端
        List<BizIOrderHeadDto> bizIOrderHeadDtoList = page.getResult().stream()
            .map(bizIOrderHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIOrderHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 待审核页面查询
     * @param bizIOrderHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<BizIOrderHeadDto>> getAeoListPaged(BizIOrderHeadParam bizIOrderHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalList(bizIOrderHeadParam.getBusinessType());
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        bizIOrderHeadParam.setIds(ids);

        // 启用分页查询
        BizIOrderHead bizIOrderHead = bizIOrderHeadDtoMapper.toPo(bizIOrderHeadParam);
        bizIOrderHead.setTradeCode(userInfo.getCompany());
        Page<BizIOrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIOrderHeadMapper.getAeoList( bizIOrderHead));
        // 将PO转为DTO返回给前端
        List<BizIOrderHeadDto> bizIOrderHeadDtoList = page.getResult().stream()
                .map(bizIOrderHeadDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizIOrderHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIOrderHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIOrderHeadDto insert(BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        BizIOrderHead bizIOrderHead = bizIOrderHeadDtoMapper.toPo(bizIOrderHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIOrderHead.setSid(sid);
        bizIOrderHead.setInsertUser(userInfo.getUserNo());
        bizIOrderHead.setInsertUserName(userInfo.getUserName());
        bizIOrderHead.setInsertTime(new Date());
        bizIOrderHead.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIOrderHeadMapper.insert(bizIOrderHead);
        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIOrderHeadDtoMapper.toDto(bizIOrderHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIOrderHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIOrderHeadDto update(BizIOrderHeadParam bizIOrderHeadParam,List<BizIOrderListParam> listParams, UserInfoToken userInfo) {
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(bizIOrderHeadParam.getSid());
        // 获取之前签订日期
        Date dateOfSigningOld = bizIOrderHead.getDateOfSigning();

        // 校验是否产生后续单据
        checkData(bizIOrderHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");

        bizIOrderHeadDtoMapper.updatePo(bizIOrderHeadParam, bizIOrderHead);
        // 校验订单号是否重复
        int orderCount = bizIOrderHeadMapper.checkOrderNo(bizIOrderHead.getOrderNo(), bizIOrderHead.getSid(),userInfo.getCompany());
        if (orderCount > 0){
            throw new ErrorException(400, XdoI18nUtil.t("订单号已经存在！"));
        }
        // 校验订单号是否符合规范
        if (StringUtils.isNotBlank(bizIOrderHead.getOrderNo())){
            // 订单号需要包含 - 并且 -后面是两位流水号  例如 CB2407-01
            if (!bizIOrderHead.getOrderNo().contains("-")){
                throw new ErrorException(400, XdoI18nUtil.t("订单号格式不正确！"));
            }
            // 判断 - 后面是否是两位流水号
            String[] split = bizIOrderHead.getOrderNo().split("-");
            if (split.length > 1){
                // 判断 是否是两位字符
                if (split[1].length() != 2){
                    throw new ErrorException(400, XdoI18nUtil.t("订单号格式不正确！"));
                }
            }else if (split.length == 1){
                throw new ErrorException(400, XdoI18nUtil.t("订单号格式不正确！"));
            }
            // 判断订单号最后两位是否可以转为数字
            try {
                // 获取字符串最后两位
                String substring = bizIOrderHead.getOrderNo().substring(bizIOrderHead.getOrderNo().length() - 2);
                // 将最后两位设置为序号
                BigDecimal bigDecimal = new BigDecimal(substring);
                bizIOrderHead.setSerialNo(bigDecimal);

            }   catch (NumberFormatException e) {
                throw new ErrorException(400, XdoI18nUtil.t("订单号格式不正确！"));
            }
        }
        bizIOrderHead.setUpdateUserName(userInfo.getUserName());
        bizIOrderHead.setUpdateUser(userInfo.getUserNo());
        bizIOrderHead.setUpdateTime(new Date());
        // 如果之前签订日期不为空，现在将签订日期变为空，那么需要报错处理
        if (dateOfSigningOld != null && bizIOrderHead.getDateOfSigning() == null){
            throw new ErrorException(400, XdoI18nUtil.t("签订日期不能为空！"));
        }
        // 如果签订日期为空，那么默认当前时间
        if (bizIOrderHead.getDateOfSigning() == null){
            bizIOrderHead.setDateOfSigning(new Date());
        }
        // 更新流向下一个节点数据
        bizIOrderHead.setIsNext("0");

        // 更新表头进货订单信息
        bizIOrderHead.setPurchaseDataStatus("0");

        // 更新数据
        int update = bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);




//        // 判断所选单据是否产生下游数据 如果产生 则不处理表体数据(true 表示产生下游数据，false 表示未产生下游数据)
//        Boolean isNextData = checkDataBoolean(bizIOrderHeadParam.getSid(), "");
//        if (!isNextData){
//
//        }
        // 更新表体数据
        if (CollectionUtils.isNotEmpty(listParams)){
            // 循环更新数据
            for (int i = 0; i < listParams.size(); i++) {
                BizIOrderListParam item = listParams.get(i);
                String message = item.canSubmit(i+1);
                if (StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }

                // 商品牌号
                String productGrade = item.getProductGrade();
                // 合同号
                String contractNo = item.getContractNo();
                // 当前编辑的sid
                String sid = item.getSid();
                // 当前数量
                BigDecimal qty = item.getQty();

                checkExcess(sid,productGrade,contractNo,userInfo.getCompany(),qty,i+1);

                // 自动计算订单表体总值
                // 自动计算总价值
                if (item.getDecPrice() != null && item.getQty() != null){
                    item.setDecTotal(item.getDecPrice().multiply(item.getQty()).setScale(5, BigDecimal.ROUND_HALF_UP));
                }
                // 循环更新表体数据
                BizIOrderList po = bizIOrderListDtoMapper.toPo(item);
                bizIOrderListMapper.updateByPrimaryKey(po);
            }
        }


        return update > 0 ? bizIOrderHeadDtoMapper.toDto(bizIOrderHead) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
       // bizIOrderHeadMapper.deleteBySids(sids);
       // 循环删除数据
        for (String sid : sids) {
            // 删除进口订单/进货/入库/销售/出库/证件/附件数据
            // int i = bizIOrderHeadMapper.deleteOrderAllData(sid);
            // 修改表头的删除逻辑
            int i = bizIOrderHeadMapper.updateDeleteFlag(sid);
        }

    }

    @Resource
    private BizIOrderListService bizIOrderListService;



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIOrderHeadDto> selectAll(BizIOrderHeadParam exportParam, UserInfoToken userInfo) {
        BizIOrderHead bizIOrderHead = bizIOrderHeadDtoMapper.toPo(exportParam);
        bizIOrderHead.setTradeCode(userInfo.getCompany());
        List<BizIOrderHeadDto> bizIOrderHeadDtos = new ArrayList<>();
        List<BizIOrderHead> bizIOrderHeadLists = bizIOrderHeadMapper.getList(bizIOrderHead);
        if (CollectionUtils.isNotEmpty(bizIOrderHeadLists)) {
           bizIOrderHeadDtos = bizIOrderHeadLists.stream().map(head -> {
                    BizIOrderHeadDto dto =  bizIOrderHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIOrderHeadDtos;
    }


    /**
     * 功能描述: 查询进口合同信息
     * 1）	单据状态为确认的
     * 2）	合同表体数量未全部生成订单的
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<List<BizIOrderExtractDto>> getIContractList(BizIOrderExtractParams params,PageParam pageParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 添加分页处理
        Page<BizIOrderExtractDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIOrderHeadMapper.getIContractList(params.getContractNo(),userInfo.getCompany()));
        // 将PO转为DTO返回给前端
        List<BizIOrderExtractDto> list = page.getResult().stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            log.error("未查询到符合数据进口合同信息！");
            return resultObject;
        }
        resultObject.setData(list);
        resultObject.setPageIndex(page.getPageNum());
        resultObject.setTotal((int) page.getTotal());
        return resultObject;
    }


    /**
     * 生成进口订单数据（将进口合同符合条数据加载到进口管理）
     * @param params 请求参数 进口合同表头sid
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject generateIOrder(String headSid,BizIOrderExtractParams params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "新增成功!");
        // 校验合同号是否为空
        Assert.notEmpty(params.getSids(),"请选择要生成的合同！");
        // 针对合同号处理
        List<String> sids = params.getSids();
        // 允许选择多份合同，选择一份以上的合同时，校验所选合同的供应商是否一致，不一致时，提示用户，不允许操作。
        // 判断选择合同号供应商是否一致 （供应商）
        List<String> supplierList = bizIOrderHeadMapper.getSuppliers(sids);
        if (supplierList.size() > 1){
            throw new ErrorException(400, XdoI18nUtil.t("所选合同供应商不一致,不允许操作！"));
        }

        // 获取计划编号，去重汇总
        String planNo =   bizIOrderHeadMapper.getPlanNo(sids);
        // 生成表头数据
        bizIOrderHeadMapper.generateIOrderHead(sids.get(0),headSid,userInfo.getCompany(),userInfo.getUserNo(),userInfo.getUserName(),planNo);
        // 判断是否传入订单号号，如果传入订单号 截取订单最后两位数字，作为序号，生成订单号
        if (StringUtils.isNotBlank(params.getOrderNo())){
            // 校验当前企业 订单号是否已经存在
            int i = bizIOrderHeadMapper.checkOrderNo(params.getOrderNo(),"",userInfo.getCompany());
            if (i > 0){
                throw new ErrorException(400, XdoI18nUtil.t("订单号已经存在！"));
            }
            String serialNo = params.getOrderNo().substring(params.getOrderNo().length()-2);
            // 获取当前订单号对应的版本号 最大序号
            BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(headSid);
            bizIOrderHead.setOrderNo(params.getOrderNo());
            bizIOrderHead.setSerialNo(new BigDecimal(serialNo));
            bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
        }else {
            // 如果是空，那么默认继续生成订单号
            // 更新订单流水号
            BizIOrderHead newBizIOrder = bizIOrderHeadMapper.selectByPrimaryKey(headSid);
            CurrentContractSerialNo  currentContractSerialNo = bizIOrderHeadMapper.getCurrentContractSerialNo(newBizIOrder.getContractPrefix(),userInfo.getCompany());
            if (currentContractSerialNo != null){
                newBizIOrder.setOrderNo(newBizIOrder.getOrderNo()+currentContractSerialNo.getSerialNoStr());
                newBizIOrder.setSerialNo(currentContractSerialNo.getSerialNo());
                bizIOrderHeadMapper.updateByPrimaryKey(newBizIOrder);
            }
        }

        for (String sid : sids) {
            // 生成表体数据
            bizIOrderHeadMapper.generateIOrderList(sid,headSid,userInfo.getCompany(),userInfo.getUserNo(),userInfo.getUserName());
        }
        // 获取当前订单号对应的版本号 最大序号
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(headSid);
        log.error("生成流水号查询参数：订单号：{}，企业编码：{}",bizIOrderHead.getOrderNo(),userInfo.getCompany());
        String serialNo = bizIOrderHeadMapper.getVersionSerialNo(bizIOrderHead.getOrderNo(),userInfo.getCompany());
        if (StringUtils.isNotBlank(serialNo)){
            bizIOrderHead.setVersionNo("版本"+serialNo);
            bizIOrderHeadMapper.updateByPrimaryKeySelective(bizIOrderHead);
        }else {
            bizIOrderHead.setVersionNo("版本1");
            bizIOrderHeadMapper.updateByPrimaryKeySelective(bizIOrderHead);
        }
        // 获取表头数据
        BizIOrderHead orderHead = bizIOrderHeadMapper.selectByPrimaryKey(headSid);
        // 判断是否存在版本号字段将版本字段过滤掉
        if (StringUtils.isNotBlank(orderHead.getVersionNo())){
            if (orderHead.getVersionNo().contains("版本")){
                orderHead.setVersionNo(orderHead.getVersionNo().replace("版本",""));
            }
        }
        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIOrderHead.getSid());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "0", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());

        resultObject.setData(orderHead);
        return resultObject;
    }


    /**
     * 订单管理表头-确认
     * @param params 订单表头信息
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmIOrderHead(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject instance = ResultObject.createInstance(true, "确认成功！");
        // 判断订单是否是作废状态，如果是作废状态，那么不允许进行确认操作
        if (StringUtils.isNotBlank(params.getDataStatus()) && "2".equals(params.getDataStatus())){
            throw new ErrorException(400, XdoI18nUtil.t("订单已经作废，不允许进行确认操作！"));
        }
        // 判断订单是否是已确认状态，如果是已确认状态，那么不允许进行确认操作
        if (StringUtils.isNotBlank(params.getDataStatus()) && "1".equals(params.getDataStatus())){
            throw new ErrorException(400, XdoI18nUtil.t("订单已经确认，不允许进行确认操作！"));
        }

        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(params.getSid());
        // 判断订单信息表头是否保存，如果没有保存，那么不允许进行确认操作
        if (bizIOrderHead.getUpdateTime() == null || StringUtils.isBlank(bizIOrderHead.getUpdateUserName())){
            throw new ErrorException(400, XdoI18nUtil.t("请先保存订单信息！"));
        }
        bizIOrderHeadDtoMapper.updatePo(params, bizIOrderHead);
        // 更新订单确认事件
        bizIOrderHead.setUpdateUser(userInfo.getUserNo());
        bizIOrderHead.setUpdateUserName(userInfo.getUserName());
        bizIOrderHead.setUpdateTime(new Date());
        bizIOrderHead.setOrderConfirmationTime(new Date());
        bizIOrderHead.setDataStatus("1");
        bizIOrderHead.setExtend1("1");
        bizIOrderHead.setIsNext("1");
        // 判断版本号是否存在版本这个字符
        if (StringUtils.isNotBlank(bizIOrderHead.getVersionNo())){
            if (!(bizIOrderHead.getVersionNo().contains("版本"))){
                bizIOrderHead.setVersionNo("版本"+bizIOrderHead.getVersionNo());
            }
        }

        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
        if (StringUtils.isNotBlank(bizIOrderHead.getVersionNo())){
            if (bizIOrderHead.getVersionNo().contains("版本")){
                bizIOrderHead.setVersionNo(bizIOrderHead.getVersionNo().replace("版本",""));
            }
        }

        // 生成进货信息
        generatePurchaseData(userInfo, bizIOrderHead);


        instance.setData(bizIOrderHead);
        return instance;
    }


    /**
     * 生成进货信息
     * @param userInfo 用户信 息
     * @param bizIOrderHead 订单表头信息
     */
    private void generatePurchaseData(UserInfoToken userInfo, BizIOrderHead bizIOrderHead) {
        // 生成进货信息
        String sid = bizIOrderHead.getSid();
        // 将数据插入到进货信息，判断是否已经生成进货信息
        int i = bizIOrderHeadMapper.getIsGeneratePurchase(sid);
        if (i == 0){
            // 进货信息表头sid
            String purchaseHeadSid = UUID.randomUUID().toString();

            TaskSequencerUtils.executeSequentially(()->{
                // 生成进货信息表头
                return bizIOrderHeadMapper.generatePurchaseHead(sid, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany(),purchaseHeadSid, bizIOrderHead.getOrderNo());
            },()->{
                // 更新订单信息表头的进货单号
                bizIOrderHeadMapper.updateOrderHeadPurchaseNo(purchaseHeadSid,sid);
                // 生成进货信息表体
                bizIOrderHeadMapper.generatePurchaseList(sid, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany(),purchaseHeadSid);
            });
        }else {
            // 删除进货信息表体（版本复制后 需要重新对数据进行更新）
            bizIOrderHeadMapper.deletePurchaseAll(sid);
            // 进货信息表头sid
            String purchaseHeadSid = UUID.randomUUID().toString();
            TaskSequencerUtils.executeSequentially(()->{
                // 生成进货信息表头
                return bizIOrderHeadMapper.generatePurchaseHead(sid, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany(),purchaseHeadSid, bizIOrderHead.getOrderNo());
            },()->{
                // 更新订单信息表头的进货单号
                bizIOrderHeadMapper.updateOrderHeadPurchaseNo(purchaseHeadSid,sid);
                // 生成进货信息表体
                bizIOrderHeadMapper.generatePurchaseList(sid, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany(),purchaseHeadSid);
            });
        }
    }


    /**
     * 获取订单表头的汇总的数据
     * @param params 订单表头信息
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getOrderHeadTotal(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIOrderHead bizIOrderHead = bizIOrderHeadDtoMapper.toPo(params);
        bizIOrderHead.setTradeCode(userInfo.getCompany());
        BizIOrderHeadTotal bizIOrderHeadTotal = bizIOrderHeadMapper.getOrderHeadTotal(bizIOrderHead);
        resultObject.setData(bizIOrderHeadTotal);
        return resultObject;
    }

    /**
     * 复制版本
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "复制版本成功！");
        if (StringUtils.isBlank(params.getSid())) {
            throw new ErrorException(400, XdoI18nUtil.t("请选择要复制的版本！"));
        }
        if (StringUtils.isBlank(params.getOrderNo())) {
            throw new ErrorException(400, XdoI18nUtil.t("所选单据订单号为空！"));
        }
        // 校验其他校验
        // 判断单据是否已经作废,如果已经作废,则直接进行复制，如果没有存在没有作废的单据，将其作废，再生成新的数据
        String oldHeadSid = params.getSid();

        // 找出有效订单状态的sid
        List<String> sids = bizIOrderHeadMapper.getValidOrdersSids(params.getOrderNo(), userInfo.getCompany());

        for (String sid : sids) {
            // 创建复制参数
            CopyVersionData copyVersionData = new CopyVersionData();
            copyVersionData.setOldOrderHeadSid(sid);
            copyVersionData.setInsertUserNo(userInfo.getUserNo());
            copyVersionData.setInsertUserName(userInfo.getUserName());
            copyVersionData.setTradeCode(userInfo.getCompany());
            // 先作废原有效数据
            cancel(copyVersionData);
        }


        // 创建复制参数
        CopyVersionData copyVersionData = new CopyVersionData();
        copyVersionData.setOldOrderHeadSid(oldHeadSid);
        copyVersionData.setInsertUserNo(userInfo.getUserNo());
        copyVersionData.setInsertUserName(userInfo.getUserName());
        copyVersionData.setTradeCode(userInfo.getCompany());
        copyVersionData.setNewOrderHeadSid(UUID.randomUUID().toString());
        copyVersionData.setPurchaseHeadSid(UUID.randomUUID().toString());
        copyVersionData.setCertificateHeadSid(UUID.randomUUID().toString());
        copyVersionData.setSellHeadSid(UUID.randomUUID().toString());
        copyVersionData.setOutHeadSid(UUID.randomUUID().toString());
        copyVersionData.setInHeadSid(UUID.randomUUID().toString());

        // 先作废原数据
        cancel(copyVersionData);

        // 复制数据
        bizIOrderHeadMapper.copyVersion(copyVersionData);


        // 获取原来的订单信息表体数据
        List<BizIOrderList> orderListList = bizIOrderListMapper.getOrderListByHeadId(oldHeadSid);
        // 获取原来的进货信息表体数据
        List<BizIPurchaseList> purchaseListList = bizIPurchaseListMapper.getPurchaseListByHeadId(oldHeadSid);
        // 获取原装箱子表数据
        List<BizIPurchaseListBox> purchaseListBoxList = bizIPurchaseListBoxMapper.getPurchaseListBoxByHeadId(oldHeadSid);
        List<BizIPurchaseListBox> tempBoxList = new ArrayList<>();

        // 处理订单表体数据
        if (CollectionUtils.isNotEmpty(orderListList)) {
            for (BizIOrderList orderList : orderListList) {
                String newSid = UUID.randomUUID().toString();
                // 根据原订单表体sid 更新 进口费用表体的sid turnover_sid,T_BIZ_EXPENSE_I_LIST
                // 获取合同表头的SID
                String orderHeadSid = orderList.getHeadId();
                // 如果版本1不需要更新,后续版本,根据次节点的数据，刷新下游节点的数据 (因为进口费用有两个提取来源 （订单表体）（进货信息表体）)
                orderList.setLastCopyListId(orderList.getSid());
                if (StringUtils.isNotBlank(orderList.getLastCopyListId())) {
                    int i = bizIOrderHeadMapper.updateTurnoverSid(orderList.getLastCopyListId(), newSid);
                }
                orderList.setSid(newSid);
                orderList.setHeadId(copyVersionData.getNewOrderHeadSid());
                orderList.setInsertUser(userInfo.getUserNo());
                orderList.setUpdateUserName(userInfo.getUserName());
                orderList.setInsertTime(new Date());
                orderList.setUpdateUser(null);
                orderList.setUpdateUserName(null);
                orderList.setUpdateTime(null);
                orderList.setDataStatus("0");
            }
        }

        // 修改进货信息表体数据
        for (BizIPurchaseList bizIPurchaseList : purchaseListList) {
            // 找到进货信息对应的装箱子表数据
            String oldSid = bizIPurchaseList.getSid();
            // 装箱子表数据
            List<BizIPurchaseListBox> boxList = purchaseListBoxList.stream().filter(box -> box.getListHeadSid().equals(oldSid)).collect(Collectors.toList());
            // 设置的新的sid
            String newSid = UUID.randomUUID().toString();
            bizIPurchaseList.setSid(newSid);
            bizIPurchaseList.setLastCopyListId(bizIPurchaseList.getSid());
            // 如果版本1不需要更新,后续版本,根据次节点的数据，刷新下游节点的数据 (因为进口费用有两个提取来源 （订单表体）（进货信息表体）)
            if (StringUtils.isNotBlank(bizIPurchaseList.getLastCopyListId())) {
                int i = bizIOrderHeadMapper.updateTurnoverSid(bizIPurchaseList.getLastCopyListId(), newSid);
            }
            bizIPurchaseList.setHeadId(copyVersionData.getPurchaseHeadSid());
            bizIPurchaseList.setContractListId(newSid);
            bizIPurchaseList.setInsertUser(copyVersionData.getInsertUserNo());
            bizIPurchaseList.setInsertUserName(copyVersionData.getInsertUserName());
            bizIPurchaseList.setInsertTime(new Date());
            bizIPurchaseList.setUpdateUser(null);
            bizIPurchaseList.setUpdateUserName(null);
            bizIPurchaseList.setUpdateTime(null);
            bizIPurchaseList.setDataStatus("0");
            // 批量修改装箱子数据
            if (CollectionUtils.isNotEmpty(boxList)) {
                for (BizIPurchaseListBox bizIPurchaseListBox : boxList) {
                    bizIPurchaseListBox.setSid(UUID.randomUUID().toString());
                    bizIPurchaseListBox.setListHeadSid(newSid);
                    bizIPurchaseListBox.setHeadId(copyVersionData.getPurchaseHeadSid());
                    bizIPurchaseListBox.setInsertUser(userInfo.getUserNo());
                    bizIPurchaseListBox.setInsertUserName(userInfo.getUserName());
                    bizIPurchaseListBox.setTradeCode(userInfo.getCompany());
                    bizIPurchaseListBox.setUpdateUser(null);
                    bizIPurchaseListBox.setUpdateUserName(null);
                    bizIPurchaseListBox.setUpdateTime(null);
                    bizIPurchaseListBox.setDataStatus("0");
                }
                tempBoxList.addAll(boxList);
            }
        }
        // 批量新增数据
        if (CollectionUtils.isNotEmpty(orderListList)) {
            orderListList.stream().forEach(item -> {
                bizIOrderListMapper.insert(item);
            });
        }
        if (CollectionUtils.isNotEmpty(purchaseListList)) {
            purchaseListList.stream().forEach(item -> {
                bizIPurchaseListMapper.insert(item);
            });
        }
        if (CollectionUtils.isNotEmpty(tempBoxList)) {
            tempBoxList.stream().forEach(box -> {
                bizIPurchaseListBoxMapper.insert(box);
            });
        }


        // 复制随附单证文件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(oldHeadSid);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(copyVersionData.getNewOrderHeadSid());
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制订单表头【"+params.getOrderNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    // 删除已经上传的归档文件(先不删除 方便排除查问题)
                    // if (CollectionUtils.isNotEmpty(tempFileList)) {
                    //     for (String fileName : tempFileList) {
                    //         try {
                    //             fileHandler.deleteFile(fileName);
                    //         }catch (Exception e1) {
                    //             e1.printStackTrace();
                    //             log.error("删除文件失败！{}", e1.getMessage());
                    //             throw new ErrorException(400, "删除归档文件异常！");
                    //         }
                    //     }
                    // }
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }
        return resultObject;
    }

    /**
     * 作废数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject cancelData(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "作废成功！");
        if (StringUtils.isBlank(params.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要作废数据！"));
        }
        // 校验其他校验
        if (StringUtils.isNotBlank(params.getDataStatus()) && params.getDataStatus().equals("2")){
            throw new ErrorException(400, XdoI18nUtil.t("已作废数据，不允许操作！"));
        }

        // 作废数据
        CopyVersionData copyVersionData = new CopyVersionData();
        copyVersionData.setOldOrderHeadSid(params.getSid());
        copyVersionData.setInsertUserNo(userInfo.getUserNo());
        copyVersionData.setInsertUserName(userInfo.getUserName());
        copyVersionData.setTradeCode(userInfo.getCompany());


        cancel(copyVersionData);

        return resultObject;

    }


    /**
     * 作废数据
     * @param copyVersionData 作废数据请求参数（抄作废参数）
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    private int cancel(CopyVersionData copyVersionData) {
        return bizIOrderHeadMapper.cancelData(copyVersionData);
    }


    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkOrderNoNotCancel(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        if (StringUtils.isBlank(params.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }
        List<String> sids = bizIOrderHeadMapper.checkOrderNoNotCancel(params.getSid());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setData(123);
            return resultObject;
        }
        return resultObject;
    }


    /**
     * 校验下游模块是否存在有效数据
     * 订单表头+订单表体（N） -> 进口费用 (1)
     * @param params 请求参数（订单表头SID）
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkNextModuleExistEffectiveData(BizIOrderHeadParam params, UserInfoToken userInfo) {
        checkData(params.getSid(),"进口费用下游模块存在有效数据，不允许操作！");
        return ResultObject.createInstance(true,"校验成功,可以继续操作！");
    }


    /**
     * 校验是否存在 下游数据
     * @param headId 订单表头SID
     */
    public void checkData(String headId,String message) {
        // TODO 校验下游数据 (颗粒度比较粗，后续待验证是否要求)
        // 校验订单表体表体是否存在 进口费用表体 (订单号+商品名称 匹配费用表体)
        int count1 = bizIOrderHeadMapper.checkNextModuleExistEffectiveDataOrder(headId);
        // 进货信息表体是否存在 进口费用表体 (进货单号+商品名称 匹配费用表体)
        int count2 = bizIOrderHeadMapper.checkNextModuleExistEffectiveDataPurchase(headId);
        // 进货信息表体是否存在 货款结算表体 (进货单号+商品名称 匹配货款结算表体)
        int count3 = bizIOrderHeadMapper.checkNextModuleExistPaymentDataPurchase(headId);
        // 进口费用表体是否存在 货款结算表体 (订单号+商品名称 匹配货款结算表体)
        int count4 = bizIOrderHeadMapper.checkNextModuleExistPaymentDataOrder(headId);
        // 根据 合同号 + 订单号 -判断下游是否产生数据，如果产生数据 则不能进行编辑
        if ((count1+count2+count3+count4) > 0){
            throw new ErrorException(400, XdoI18nUtil.t(message));
        }
    }

    public Boolean checkDataBoolean(String headId,String message) {
        // TODO 校验下游数据 (颗粒度比较粗，后续待验证是否要求)
        // 校验订单表体表体是否存在 进口费用表体 (订单号+商品名称 匹配费用表体)
        int count1 = bizIOrderHeadMapper.checkNextModuleExistEffectiveDataOrder(headId);
        // 进货信息表体是否存在 进口费用表体 (进货单号+商品名称 匹配费用表体)
        int count2 = bizIOrderHeadMapper.checkNextModuleExistEffectiveDataPurchase(headId);
        // 进货信息表体是否存在 货款结算表体 (进货单号+商品名称 匹配货款结算表体)
        int count3 = bizIOrderHeadMapper.checkNextModuleExistPaymentDataPurchase(headId);
        // 进口费用表体是否存在 货款结算表体 (订单号+商品名称 匹配货款结算表体)
        int count4 = bizIOrderHeadMapper.checkNextModuleExistPaymentDataOrder(headId);
        // 根据 合同号 + 订单号 -判断下游是否产生数据，如果产生数据 则不能进行编辑
        if ((count1+count2+count3+count4) > 0){
            return true;
        }
        return false;
    }

    public BizIOrderHeadDto print(String sid, UserInfoToken userInfo) {
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(sid);
        if(ObjectUtils.isEmpty(bizIOrderHead)){
            throw new ErrorException(400, XdoI18nUtil.t("所选订单不存在！"));
        }
        BizIOrderHeadDto bizIOrderHeadDto = bizIOrderHeadDtoMapper.toDto(bizIOrderHead);
        if(StringUtils.isNotBlank(bizIOrderHead.getPartyB())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(bizIOrderHead.getPartyB());
            List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
            if(!ObjectUtils.isEmpty(bizMerchants)){
                bizIOrderHeadDto.setPartyB(bizMerchants.get(0).getMerchantNameCn());
            }
        }
        BizIOrderListParam bizIOrderListParam = new BizIOrderListParam();
        bizIOrderListParam.setHeadId(sid);
        List<BizIOrderListDto> bizIOrderListDtos = bizIOrderListService.selectAll(bizIOrderListParam, userInfo);
        if(!ObjectUtils.isEmpty(bizIOrderListDtos)){
            bizIOrderHeadDto.setOrderList(bizIOrderListDtos);
            BigDecimal qtyR = bizIOrderListDtos.stream().map(BizIOrderListDto::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            bizIOrderHeadDto.setQty(NumberFormatterUtils.formatNumber(qtyR));
//            bizIOrderHeadDto.setQty((qtyR.stripTrailingZeros().scale() <= 0) ? qtyR.stripTrailingZeros().toPlainString() + ".00" : qtyR.toPlainString());
            BigDecimal decTR = bizIOrderListDtos.stream().map(BizIOrderListDto::getDecTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            bizIOrderHeadDto.setDecTotal(NumberFormatterUtils.formatNumber(decTR));
//            bizIOrderHeadDto.setDecTotal((decTR.stripTrailingZeros().scale() <= 0) ? decTR.stripTrailingZeros().toPlainString() + ".00" : decTR.toPlainString());
        }
        return bizIOrderHeadDto;
    }

    public ResultObject confirmOrderHead(BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(bizIOrderHeadParam.getSid());
        if(ObjectUtils.isEmpty(bizIOrderHead)){
            throw new ErrorException(400, XdoI18nUtil.t("所选销售信息不存在！"));
        }
        if ("1".equals(bizIOrderHead.getSalesDataStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        bizIOrderHead.setSalesDataStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIOrderHead.setUpdateUser(userInfo.getUserNo());
        bizIOrderHead.setUpdateTime(new Date());
        bizIOrderHead.setUpdateUserName(userInfo.getLoginName());

        List<BizISellHead> select = bizISellHeadMapper.select(new BizISellHead() {{
            setHeadId(bizIOrderHeadParam.getSid());
        }});
        BizISellHead bizISellHead = select.get(0);
        bizISellHead.setSalesDocumentStatus("1");
        bizISellHead.setUpdateUser(userInfo.getUserNo());
        bizISellHead.setUpdateTime(new Date());
        bizISellHead.setSalesDataConfirmationTime(new Date());
        bizISellHead.setUpdateUserName(userInfo.getUserName());
        bizISellHeadMapper.updateByPrimaryKey(bizISellHead);

        //触发对接用友系统接口
        List<BizISellList> list = bizISellListMapper.select(new BizISellList() {{
            setTradeCode(userInfo.getCompany());
            setHeadId(bizISellHead.getSid());
        }});
        //用友----销售
        GwstdHttpConfig gwstdHttpConfig = gwstdHttpConfigMapper.selectByType("YonyouState");
        if(gwstdHttpConfig != null && "1".equals(gwstdHttpConfig.getServiceUrl())) {
            //销售
            sendSellYonyou(bizISellHead,list,userInfo);
        }
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
        return result;
    }

    private void sendSellYonyou(BizISellHead head, List<BizISellList> list, UserInfoToken userInfo) {
        String id = UUID.randomUUID().toString().replace("-", StringUtils.EMPTY);
        OBillJck oBillJck = headMessageSell(head, list, userInfo,id);
        List<OBillBJck> oBillBJcks = listMessageSell(head, list, userInfo,id);
        //存储信息
        try {
            Map<String, Object> stringObjectMap = thirdPartyDbService.convertToMap(oBillJck);
            thirdPartyDbService.insertData("O_BILL_JCK",stringObjectMap);
            List<Map<String, Object>> maps = thirdPartyDbService.convertListToMapList(oBillBJcks);
            thirdPartyDbService.batchInsertData("O_BILL_B_JCK",maps);
        } catch (IllegalAccessException e) {
            throw new RuntimeException("发生用友失败！");
        }
    }

    private List<OBillBJck> listMessageSell(BizISellHead head, List<BizISellList> list, UserInfoToken userInfo,String id) {
        List<OBillBJck> oBillBJcks = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            long[] seqNos = this.thirdPartyDbService.getSequenceNextValue("O_BILL_B_JCK_SEQ", list.size());
            for (int i = 0; i < list.size(); i++) {
                BizISellList bizISellList = list.get(i);
                OBillBJck oBillBJck = new OBillBJck();
                //--字段名称---字段名-----------取值
                //关联主表外键  billid         接口程序赋值
                oBillBJck.setBillid(id);
                //存货编码	  inventory      根据付款通知表体-商品名称关联【物料信息】的条形码
                List<BizMaterialInformation> select = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setTradeCode(userInfo.getCompany());
                    setGname(bizISellList.getTradeName());
                }});
                if(CollectionUtils.isNotEmpty(select)){
                    oBillBJck.setInventory(select.get(0).getBarCode());
                }
                //数量	      tnumber        出库回单表体-数量
                oBillBJck.setTnumber(bizISellList.getQuantity());
                //计量单位	  mainunit       关联取值用友的计量单位代码
                List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setCustomParamCode(bizISellList.getUnit());
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                if(CollectionUtils.isNotEmpty(selectCurr)){
                    oBillBJck.setMainunit(selectCurr.get(0).getUnitYonyou());
                    oBillBJck.setNameMainunit(selectCurr.get(0).getParamsName());
                }
                //单价	      price          0
                oBillBJck.setPrice(new BigDecimal(0));
                //不含税金额	  notaxmoney     费用表体-无税金额
                oBillBJck.setNotaxmoney(bizISellList.getTaxNotIncluded());
                //金额	      totalmoney     表体-价税合计
                oBillBJck.setTotalmoney(bizISellList.getTotalValueTax());
                //货价	      hj             费用表体-税额
                oBillBJck.setHj(bizISellList.getAmountOfTax());
                //存货名称	  name_invmandoc 入库回单表体-商品名称
                oBillBJck.setNameInvmandoc(bizISellList.getTradeName());
                //系统时间	  ts             接口数据发送时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date());
                oBillBJck.setTs(formattedDate);
                //客商编码	  shipcustname   出库回单表头-供应商（取财务系统编码）
                oBillBJck.setShipcustname(head.getPurchasingUnit());
                //发票号	      billno_bt      入库回单表体-进口发票号
                oBillBJck.setBillnoBt(bizISellList.getSalesInvoiceNumber());
                //客商编码	  pk_shipcust    出库回单表头-供应商（取财务系统编码）
                List<BizMerchant> selectMerchant = bizMerchantMapper.select(new BizMerchant() {{
                    setMerchantCode(head.getPurchasingUnit());
                }});
                if(CollectionUtils.isNotEmpty(selectMerchant)){
                    oBillBJck.setPkShipcust(select.get(0).getMerchantNameCn());
                }
                oBillBJck.setMqLsh((int) seqNos[i]);
                //--字段名称---字段名-----------默认值
                //收支项目编码 costsubj         2001
                oBillBJck.setCostsubj("2001");
                //收支项目名称 name_costsubj    传固定值：货款
                oBillBJck.setNameCostsubj("货款");
                //           mq_op            i
                oBillBJck.setMqOp("i");
                //           mq_st            0
                oBillBJck.setMqSt("0");
                //           mq_count         1
                oBillBJck.setMqCount(1);
                //           pk_corp          1022
                oBillBJck.setPkCorp("1022");
                oBillBJck.setDr(0);
                oBillBJcks.add(oBillBJck);
            }
        }
        return oBillBJcks;
    }

    private OBillJck headMessageSell(BizISellHead head, List<BizISellList> list, UserInfoToken userInfo,String id) {
        OBillJck oBillJck = new OBillJck();
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(head.getHeadId());
//--------字段名称----字段名------------取值
        //单据主键　	billid           接口程序赋值
        oBillJck.setBillid(id);
        //单据编号　	billcode         表头-出库回单编号
        oBillJck.setBillcode(head.getPurchaseOrderNumber());
        //单据日期	billdate         表头-业务日期
        if(head.getBusinessDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(head.getBusinessDate());
            oBillJck.setBilldate(formattedDate);
        }
        //业务人员	person           表头-制单人登录用户名
        oBillJck.setPerson(head.getInsertUser());
        //制单人员	maker            表头-制单人登录用户名
        oBillJck.setMaker(head.getInsertUser());
        //客商编码	cust             表头-购货单位（取财务系统编码）
        oBillJck.setCust(head.getPurchasingUnit());
        //业务员名称	name_psndoc      制单人用户姓名
        oBillJck.setNamePsndoc(head.getInsertUserName());
        //操作员名称	name_operator    制单人用户姓名
        oBillJck.setNameOperator(head.getInsertUserName());
        //客商名称	name_cumandoc    出库回单表头-提货人（名称）
        if(StringUtil.isNotEmpty(head.getPurchasingUnit())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(head.getPurchasingUnit());
            //查询出全部的客商信息
            List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
            oBillJck.setNameCumandoc(select1.get(0).getMerchantNameCn());
        }
        //系统时间	ts               接口数据发送时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        oBillJck.setTs(formattedDate);

        if(CollectionUtils.isNotEmpty(list)) {
            //表体行数	row_count        接口表赋值：表体数据的行数
            oBillJck.setRowCount(list.size());
        }
        oBillJck.setMqLsh((int) this.thirdPartyDbService.getSequenceNextValue("O_BILL_JCK_SEQ", 1)[0]);
//--------字段名称-----字段名-----------默认值
        //单据类型	 rd_type           T3
        oBillJck.setRdType("T3");
        //库存组织	 rdcenter          JCK01
        oBillJck.setRdcenter("JCK01");
        //公司名称	 company           中国烟草上海进出口有限责任公司
        oBillJck.setCompany("中国烟草上海进出口有限责任公司");
        //部门编码	 deptdoc           04
        oBillJck.setDeptdoc("04");
        //库存组织名称 name_calbody      个别计价
        oBillJck.setNameCalbody("个别计价");
        //部门名称	 name_deptdoc      业务一部
        oBillJck.setNameDeptdoc("业务一部");
        //           mq_op             i
        oBillJck.setMqOp("1");
        //           mq_st             0
        oBillJck.setMqSt("0");
        //           mq_count          1
        oBillJck.setMqCount(1);
        //           pk_corp           1022
        oBillJck.setPkCorp("1022");
        oBillJck.setDr(0);
        //币种编码	 currtypecode      CNY
        oBillJck.setCurrtypecode("CNY");
        //币种名称	 currtypename      人民币
        oBillJck.setCurrtypename("人民币");
        return oBillJck;
    }

    public ResultObject confirmReceiptHead(BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(bizIOrderHeadParam.getSid());
        if(ObjectUtils.isEmpty(bizIOrderHead)){
            throw new ErrorException(400, XdoI18nUtil.t("所选出库回单信息不存在！"));
        }
        if ("1".equals(bizIOrderHead.getOutboundReceiptStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        if ("2".equals(bizIOrderHead.getOutboundReceiptStatus())){
            throw new ErrorException(400, "该数据已经作废，无法确认");
        }
        bizIOrderHead.setOutboundReceiptStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIOrderHead.setUpdateUser(userInfo.getUserNo());
        bizIOrderHead.setUpdateTime(new Date());
        bizIOrderHead.setUpdateUserName(userInfo.getLoginName());

        List<BizIReceiptHead> select = bizIReceiptHeadMapper.select(new BizIReceiptHead() {{
            setHeadId(bizIOrderHeadParam.getSid());
        }});
        BizIReceiptHead bizIReceiptHead = select.get(0);
        bizIReceiptHead.setOutstockDocumentStatus("1");
        bizIReceiptHead.setUpdateUser(userInfo.getUserNo());
        bizIReceiptHead.setUpdateTime(new Date());
        bizIReceiptHead.setUpdateUserName(userInfo.getUserName());
        bizIReceiptHeadMapper.updateByPrimaryKey(bizIReceiptHead);

        //todo 发送用友
        List<BizIReceiptList> list = bizIReceiptListMapper.select(new BizIReceiptList() {{
            setTradeCode(userInfo.getCompany());
            setHeadId(bizIReceiptHead.getSid());
        }});
        //用友----出库
        GwstdHttpConfig gwstdHttpConfig = gwstdHttpConfigMapper.selectByType("YonyouState");
        if(gwstdHttpConfig != null && "1".equals(gwstdHttpConfig.getServiceUrl())) {
        sendYonyou(bizIReceiptHead,list,userInfo);
        }
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);


        return result;
    }

    private void sendYonyou(BizIReceiptHead head, List<BizIReceiptList> list, UserInfoToken userInfo) {
        String id = UUID.randomUUID().toString().replace("-", StringUtils.EMPTY);
        OBillJck oBillJck = headMessage(head, list, userInfo,id);
        List<OBillBJck> oBillBJcks = listMessage(head, list, userInfo,id);
        //存储信息
        try {
            Map<String, Object> stringObjectMap = thirdPartyDbService.convertToMap(oBillJck);
            thirdPartyDbService.insertData("O_BILL_JCK",stringObjectMap);
            List<Map<String, Object>> maps = thirdPartyDbService.convertListToMapList(oBillBJcks);
            thirdPartyDbService.batchInsertData("O_BILL_B_JCK",maps);
        } catch (IllegalAccessException e) {
            throw new RuntimeException("发生用友失败！");
        }
    }

    private OBillJck headMessage(BizIReceiptHead head, List<BizIReceiptList> list, UserInfoToken userInfo,String id) {
        OBillJck oBillJck = new OBillJck();
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(head.getHeadId());
        //获取入库回单头
        List<BizIWarehouseReceiptHead> receiptHead = bizIWarehouseReceiptHeadMapper.select(new BizIWarehouseReceiptHead() {{
            setParentId(bizIOrderHead.getSid());
        }});
        //获取入库回单体
        List<BizIWarehouseReceiptList> bizIWarehouseReceiptLists = bizIWarehouseReceiptListMapper.select(new BizIWarehouseReceiptList() {{
            setParentId(receiptHead.get(0).getSid());
        }});
//--------字段名称----字段名------------取值
        //单据主键　	billid           接口程序赋值
        oBillJck.setBillid(id);
        //单据编号　	billcode         出库回单表头-出库回单编号
        oBillJck.setBillcode(head.getReceiptNumber());
        //单据日期	billdate         出库回单表头-出库日期
        if(head.getDeliveryDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(head.getDeliveryDate());
            oBillJck.setBilldate(formattedDate);
        }
        //业务人员	person           表头-制单人登录用户名
        oBillJck.setPerson(head.getInsertUser());
        //制单人员	maker            表头-制单人登录用户名
        oBillJck.setMaker(head.getInsertUser());
        //客商编码	cust             出库回单表头-提货人（取财务系统编码）
        oBillJck.setCust(head.getConsignee());
        //业务员名称	name_psndoc      制单人用户姓名
        oBillJck.setNamePsndoc(head.getInsertUserName());
        //操作员名称	name_operator    制单人用户姓名
        oBillJck.setNameOperator(head.getInsertUserName());
        //客商名称	name_cumandoc    出库回单表头-提货人（名称）
        if(StringUtil.isNotEmpty(head.getConsignee())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(head.getConsignee());
            //查询出全部的客商信息
            List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
            oBillJck.setNameCumandoc(select1.get(0).getMerchantNameCn());
        }
        //系统时间	ts               接口数据发送时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        oBillJck.setTs(formattedDate);
        //仓库代码	storage          出库回单表头-仓库（代码）
        oBillJck.setStorage(head.getWarehouse());
        //仓库名称	name_storage     出库回单表头-仓库名称
        if(StringUtil.isNotEmpty(head.getWarehouse())){
            List<Storehouse> select = storehouseMapper.select(new Storehouse() {{
                setTradeCode(userInfo.getCompany());
                setParamCode(head.getWarehouse());
            }});
            oBillJck.setNameStorage(select.get(0).getStorehouseName());
        }
        if(CollectionUtils.isNotEmpty(list)) {
        //总费用	    totalmoney       汇总出库回单表体-金额
            oBillJck.setTotalmoney(list.stream().map(BizIReceiptList::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        //表体行数	row_count        接口表赋值：表体数据的行数
            oBillJck.setRowCount(list.size());
        }
        if(CollectionUtils.isNotEmpty(bizIWarehouseReceiptLists)){
            //发票号     billno           入库回单表体-进口发票号码
            oBillJck.setBillno(bizIWarehouseReceiptLists.stream().map(BizIWarehouseReceiptList::getInvoiceNumber).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.joining(",")));
        }
        oBillJck.setMqLsh((int) this.thirdPartyDbService.getSequenceNextValue("O_BILL_JCK_SEQ", 1)[0]);
//--------字段名称-----字段名-----------默认值
        //单据类型	 rd_type           S1
        oBillJck.setRdType("S1");
        //库存组织	 rdcenter          JCK01
        oBillJck.setRdcenter("JCK01");
        //公司名称	 company           中国烟草上海进出口有限责任公司
        oBillJck.setCompany("中国烟草上海进出口有限责任公司");
        //部门编码	 deptdoc           04
        oBillJck.setDeptdoc("04");
        //库存组织名称 name_calbody      个别计价
        oBillJck.setNameCalbody("个别计价");
        //部门名称	 name_deptdoc      业务一部
        oBillJck.setNameDeptdoc("业务一部");
        //           mq_op             i
        oBillJck.setMqOp("1");
        //           mq_st             0
        oBillJck.setMqSt("0");
        //           mq_count          1
        oBillJck.setMqCount(1);
        //           pk_corp           1022
        oBillJck.setPkCorp("1022");
        //币种编码	 currtypecode      CNY
        oBillJck.setCurrtypecode("CNY");
        //币种名称	 currtypename      人民币
        oBillJck.setCurrtypename("人民币");
        return oBillJck;
    }

    private List<OBillBJck> listMessage(BizIReceiptHead head, List<BizIReceiptList> list, UserInfoToken userInfo,String id) {
        List<OBillBJck> oBillBJcks = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            long[] seqNos = this.thirdPartyDbService.getSequenceNextValue("O_BILL_B_JCK_SEQ", list.size());
            for (int i = 0; i < list.size(); i++) {
                BizIReceiptList bizIReceiptList = list.get(i);
                OBillBJck oBillBJck = new OBillBJck();
                BizIWarehouseReceiptList bizIWarehouseReceiptList = bizIWarehouseReceiptListMapper.selectByPrimaryKey(bizIReceiptList.getSid());
                //--字段名称---字段名-----------取值
                //关联主表外键  billid         接口程序赋值
                oBillBJck.setBillid(id);
                //存货编码	  inventory      根据出库回单表体-商品名称关联【物料信息】的条形码
                List<BizMaterialInformation> select = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setTradeCode(userInfo.getCompany());
                    setGname(bizIWarehouseReceiptList.getGoodsName());
                }});
                if(CollectionUtils.isNotEmpty(select)){
                    oBillBJck.setInventory(select.get(0).getBarCode());
                }
                //数量	      tnumber        出库回单表体-数量
                oBillBJck.setTnumber(bizIReceiptList.getActualQuantityIssued());
                //计量单位	  mainunit       关联取值用友的计量单位代码
                List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setCustomParamCode(bizIReceiptList.getUnit());
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                if(CollectionUtils.isNotEmpty(selectCurr)){
                    oBillBJck.setMainunit(selectCurr.get(0).getUnitYonyou());
                    oBillBJck.setNameMainunit(selectCurr.get(0).getParamsName());
                }
                //单价	      price          出库回单表体-单价
                oBillBJck.setPrice(bizIReceiptList.getDecPrice());
                //不含税金额	  notaxmoney     出库回单表体-金额
                oBillBJck.setNotaxmoney(bizIReceiptList.getAmount());
                //金额	      totalmoney     出库回单表体-金额
                oBillBJck.setTotalmoney(bizIReceiptList.getAmount());
                //货价	      hj             出库回单表体-金额
                oBillBJck.setHj(bizIReceiptList.getAmount());
                //存货名称	  name_invmandoc 入库回单表体-商品名称
                oBillBJck.setNameInvmandoc(bizIReceiptList.getTradeName());
                //系统时间	  ts             接口数据发送时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date());
                oBillBJck.setTs(formattedDate);
                //客商编码	  shipcustname   出库回单表头-供应商（取财务系统编码）
                oBillBJck.setShipcustname(head.getSupplier());
                //订单号	      orderno        出库回单表头-订单号
                oBillBJck.setOrderno(head.getOrderNumber());
                //发票号	      billno_bt      入库回单表体-进口发票号
                oBillBJck.setBillnoBt(bizIWarehouseReceiptList.getInvoiceNumber());
                //客商编码	  pk_shipcust    出库回单表头-供应商（取财务系统编码）
                List<BizMerchant> selectMerchant = bizMerchantMapper.select(new BizMerchant() {{
                    setMerchantCode(head.getSupplier());
                }});
                if(CollectionUtils.isNotEmpty(selectMerchant)){
                    oBillBJck.setPkShipcust(select.get(0).getMerchantNameCn());
                }
                if(StringUtil.isNotEmpty(head.getSupplier())){
                    BiClientInformation biClientInformation = new BiClientInformation();
                    biClientInformation.setTradeCode(userInfo.getCompany());
                    biClientInformation.setCustomerCode(head.getSupplier());
                    biClientInformation.setStatus("0");
                    //查询出全部的客商信息
                    List<BiClientInformation> selectSupplier = biClientInformationMapper.select(biClientInformation);
                    oBillBJck.setPkShipcust(selectSupplier.get(0).getCompanyName());
                }
                oBillBJck.setMqLsh((int) seqNos[i]);
                //--字段名称---字段名-----------默认值
                //税额	     taxmoney         0
                oBillBJck.setTaxmoney(new BigDecimal("0"));
                //关税	     gs               0
                oBillBJck.setGs(new BigDecimal("0"));
                //消费税	     xfs              0
                oBillBJck.setXfs(new BigDecimal("0"));
                //海运费	     hyf              0
                oBillBJck.setHyf(new BigDecimal("0"));
                //其他费用	 qtfy             0
                oBillBJck.setQtfy(new BigDecimal("0"));
                //收支项目编码 costsubj         1003
                oBillBJck.setCostsubj("1003");
                //收支项目名称 name_costsubj    自营进口销售收入
                oBillBJck.setNameCostsubj("自营进口销售收入");
                //           mq_op            i
                oBillBJck.setMqOp("i");
                //           mq_st            0
                oBillBJck.setMqSt("0");
                //           mq_count         1
                oBillBJck.setMqCount(1);
                //           pk_corp          1022
                oBillBJck.setPkCorp("1022");
                oBillBJck.setDr(0);
                oBillBJcks.add(oBillBJck);
            }
        }
        return oBillBJcks;
    }
    /**
     * 获取订单供应商
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getOrderSupplierList(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizIOrderHeadMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }


    /**
     * 根据企业代码获取供应商信息
     * @param userInfoToken 用户信息
     * @return 返回结果 {label:供应商名称,value:供应商代码}
     */
    public List<Map<String,String>> getSupplierList(UserInfoToken userInfoToken) {
        return bizIOrderHeadMapper.getOrderSupplierList(userInfoToken.getCompany());
    }


    /**
     * 重启作废单据号
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject rebootOrderNo(BizIOrderExtractParams params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "重启成功！");
        if (CollectionUtils.isEmpty(params.getSids())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要合同号！"));
        }
        // 获取选择第一个sid 查询合
        String contractNoSid = params.getSids().get(0);
        String contractNo  = bizIOrderHeadMapper.getContractNo(contractNoSid,userInfo.getCompany());
        if (StringUtils.isBlank(contractNo)){
            throw new ErrorException(400, XdoI18nUtil.t("所选合同号不存在！"));
        }
        // 根据合同号获取 作废或者删除的订单号
        List<String> orderNoList = bizIOrderHeadMapper.getOrderNoList(contractNo,userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(orderNoList)){
            RebootContractList rebootContractList = new RebootContractList();
            rebootContractList.setMinContractNo(orderNoList.get(0));
            rebootContractList.setRebootContractList(orderNoList);
            resultObject.setData(rebootContractList);
            return resultObject;
        }
        return resultObject;
    }

    /**
     * 获取订单表头流向各个模块的情况数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkOrderHeadIsNextModule(BizIOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        String sid = params.getSid();
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(sid);
        OrderHeadIsNextModuleDto orderHeadIsNextModuleDto = new OrderHeadIsNextModuleDto();
        // 判断是否显示证件信息
        if (bizIOrderHead.getUpdateTime() != null){
            orderHeadIsNextModuleDto.setShowBodyDocumentHeadCount(1);
        }else {
            orderHeadIsNextModuleDto.setShowBodyDocumentHeadCount(0);
        }

        // 是否显示进货信息
        if (StringUtils.isNotBlank(bizIOrderHead.getIsNext()) && bizIOrderHead.getIsNext().equals("1")){
            orderHeadIsNextModuleDto.setShowBodyPurchaseHeadCount(1);
        }else {
            orderHeadIsNextModuleDto.setShowBodyPurchaseHeadCount(0);
        }

        // 是否显示入库信息
        String purchaseHeadIsNext  = bizIOrderHeadMapper.getPurchaseHeadIsNext(sid);
        if (StringUtils.isNotBlank(purchaseHeadIsNext) && purchaseHeadIsNext.equals("1")){
            orderHeadIsNextModuleDto.setShowBodyWarehouseReceiptHeadCount(1);
        }else {
            orderHeadIsNextModuleDto.setShowBodyWarehouseReceiptHeadCount(0);
        }


        // 是否显示销售或者出库信息
        String sellOrOutHeadIsNext  = bizIOrderHeadMapper.getSellOrOutHead(sid);
        if (StringUtils.isNotBlank(sellOrOutHeadIsNext) && sellOrOutHeadIsNext.equals("1")){
            orderHeadIsNextModuleDto.setShowBodyReceiptSellCount(1);
        }else{
            orderHeadIsNextModuleDto.setShowBodyReceiptSellCount(0);
        }


        // // 进货信息是否存在数据
        // int i  = bizIOrderHeadMapper.getPurchaseCount(sid);
        // orderHeadIsNextModuleDto.setShowBodyPurchaseHeadCount(i);
        // if (StringUtils.isBlank(bizIOrderHead.getExtend1())){
        //     // 只保存流转了，但是没有确认，不显示进货信息
        //     orderHeadIsNextModuleDto.setShowBodyPurchaseHeadCount(0);
        // }
        // // 入库信息是否存在数据
        // int j = bizIOrderHeadMapper.getReceiptHeadCount(sid);
        // orderHeadIsNextModuleDto.setShowBodyWarehouseReceiptHeadCount(j);
//
        // // 销售或者出库信息是否存在数据
        // int k = bizIOrderHeadMapper.getSellOrOutHeadCount(sid);
        // orderHeadIsNextModuleDto.setShowBodyReceiptSellCount(k);

        resultObject.setData(orderHeadIsNextModuleDto);
        return resultObject;
    }

    public ResultObject confirmRefreshList(BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "更新成功！");
        //点击操作时，根据表体商品名称关联【物料信息】刷新”不含税单价“栏位值，
        //并根据单价再次计算”不含税金额“、根据金额再次计算”税额“，及”价税合计“
        BigDecimal rate = null;
        List<BizISellList> select = bizISellListMapper.select(new BizISellList() {{
            setHeadId(bizIOrderHeadParam.getSid());
        }});
        if(CollectionUtils.isNotEmpty(select)){
            for (BizISellList bizISellList : select) {
                //获取物料信息数据
                List<BizMaterialInformation> select1 = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setGname(bizISellList.getTradeName());
                    setTradeCode(bizISellList.getTradeCode());
                }});
                if(CollectionUtils.isNotEmpty(select1)){
                    BizMaterialInformation bizMaterialInformation = select1.get(0);
                    rate = bizMaterialInformation.getTaxRate();
                    BigDecimal divide = rate.divide(new BigDecimal("100"));
                    //计算方式
                    //不含税单价：物料信息对应不含税单价
                    bizISellList.setUnitPriceExcludingTax(bizMaterialInformation.getPriceExcludingTax());
                    //税额：物料信息对应税率
                    bizISellList.setAmountOfTax(bizISellList.getQuantity().multiply(divide).setScale(2, RoundingMode.HALF_UP));
                    //不含税金额：表体数量*不含税单价
                    if(bizISellList.getQuantity() != null && bizISellList.getUnitPriceExcludingTax() != null){
                        bizISellList.setTaxNotIncluded(bizISellList.getQuantity().multiply(bizISellList.getUnitPriceExcludingTax()).setScale(2, RoundingMode.HALF_UP));
                    }
                    //价税合计：税额+不含税金额
                    if(bizISellList.getTaxNotIncluded() != null && bizISellList.getAmountOfTax() != null){
                        bizISellList.setTotalValueTax(bizISellList.getTaxNotIncluded().add(bizISellList.getAmountOfTax()).setScale(2, RoundingMode.HALF_UP));
                    }
                    //更新
                    bizISellListMapper.updateByPrimaryKey(bizISellList);
                }
            }
        }
        //更新表头
        BizISellHead bizISellHead = bizISellHeadMapper.selectByPrimaryKey(bizIOrderHeadParam.getSid());
        if(rate != null){
            bizISellHead.setTaxRate(rate);
            bizISellHeadMapper.updateByPrimaryKey(bizISellHead);
        }
        return resultObject;
    }



    /**
     * 校验数量是否超出合同数量
     * @param sid 当前编辑的sid
     * @param productGrade 商品牌号
     * @param contractNo 合同号
     * @param tradeCode 企业代码
     * @param qty 当前编辑数量
     */
    public void checkExcess(String sid,String productGrade,String contractNo,String tradeCode,BigDecimal qty,int index){
        // 获取合同原始数量
        BigDecimal originalQty = bizIOrderListMapper.getContractOriginalQty(productGrade,contractNo,tradeCode);
        if (originalQty == null){
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范："+"未匹配到有效合同数据！"));
        }
        // 获取除了当前编辑的数据之外的数量
        BigDecimal totalQty = bizIOrderListMapper.getOrderOriginalQty(sid,productGrade,contractNo,tradeCode);
        BigDecimal total = BigDecimal.ZERO;
        if (totalQty != null){
            total = totalQty.add(qty);
        }else {
            total = qty;
        }

        // 判断数量是否超出合同数量
        if (total.compareTo(originalQty) > 0){
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范："+"数量不允许超出该合同号下已形成订单的剩余数量！"));
        }
    }

    public ResultObject confirmIncoming(BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(bizIOrderHeadParam.getSid());
        if(ObjectUtils.isEmpty(bizIncomingGoodsHead)){
            throw new ErrorException(400, XdoI18nUtil.t("所选销售信息不存在！"));
        }
        // if ("1".equals(bizIncomingGoodsHead.getSalesDocumentStatus())){
        //     throw new ErrorException(400, "该数据已经确认，无需重复操作");
        // }
        bizIncomingGoodsHead.setSalesDocumentStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIncomingGoodsHead.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsHead.setUpdateTime(new Date());
        bizIncomingGoodsHead.setUpdateUserName(userInfo.getLoginName());

        List<BizISellHead> select = bizISellHeadMapper.select(new BizISellHead() {{
            setHeadId(bizIOrderHeadParam.getSid());
        }});
        BizISellHead bizISellHead = select.get(0);
        bizISellHead.setSalesDocumentStatus("1");
        bizISellHead.setUpdateUser(userInfo.getUserNo());
        bizISellHead.setUpdateTime(new Date());
        bizISellHead.setSalesDataConfirmationTime(new Date());
        bizISellHead.setUpdateUserName(userInfo.getUserName());
        bizISellHeadMapper.updateByPrimaryKey(bizISellHead);

        //todo 发送用友
        tBizIncomingGoodsHeadMapper.updateByPrimaryKey(bizIncomingGoodsHead);

        return result;
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        //更新表头状态
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));
        if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(bizIOrderHead.getApprStatus())
                && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(bizIOrderHead.getApprStatus())){
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        bizIOrderHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        bizIOrderHead.setUpdateUser(userInfo.getUserNo());
        bizIOrderHead.setUpdateTime(new Date());
        bizIOrderHead.setUpdateUserName(userInfo.getUserName());
        //记录flowInstanceId
        bizIOrderHead.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIOrderHead.getSid());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
            if (nextNodeInfoVo.isFinish()) {
                bizIOrderHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                bizIOrderHead.setUpdateUser(userInfo.getUserNo());
                bizIOrderHead.setUpdateTime(new Date());
                bizIOrderHead.setUpdateUserName(userInfo.getUserName());
                bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIOrderHead.getSid());
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            //仅单条退回
            //回退到发起人
            BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));

            bizIOrderHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            bizIOrderHead.setUpdateUser(userInfo.getUserNo());
            bizIOrderHead.setUpdateTime(new Date());
            bizIOrderHead.setUpdateUserName(userInfo.getUserName());
            bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIOrderHead.getSid());
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return bizIOrderHeadMapper.getFlowList(ids);
    }
}