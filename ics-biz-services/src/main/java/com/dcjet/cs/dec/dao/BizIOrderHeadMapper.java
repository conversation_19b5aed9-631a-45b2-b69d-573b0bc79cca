package com.dcjet.cs.dec.dao;

import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dto.dec.BizIOrderExtractDto;
import com.dcjet.cs.dto.dec.BizIOrderHeadTotal;
import com.dcjet.cs.dto.dec.CopyVersionData;
import com.dcjet.cs.dto.dec.CurrentContractSerialNo;
import com.xdo.common.token.UserInfoToken;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * 进口管理-订单信息表头Mapper
 */
public interface BizIOrderHeadMapper extends Mapper<BizIOrderHead>{

    /**
     * 查询获取数据
     * @param bizIOrderHead
     * @return
     */
    List<BizIOrderHead> getList(BizIOrderHead bizIOrderHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    /**
     * 查询进口合同信息
     * @param tradeCode 公司编码
     * @return 返回结果
     */
    List<BizIOrderExtractDto> getIContractList(@Param("contractNo")String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 生成进口订单表头
     * @param sid 进口合同表头sid
     * @param headSid 表头sid
     * @param tradeCode 企业代码
     * @param userNo 用户账号
     * @param userName 用户名称
     * @param planNo 计划编号
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int generateIOrderHead(@Param("sid")String sid,
                           @Param("headSid") String headSid,
                           @Param("tradeCode") String tradeCode,
                           @Param("userNo") String userNo,
                           @Param("userName") String userName,
                           @Param("planNo") String planNo);


    /**
     * @param sid 进口合同表头sid
     * @param headSid 表头sid
     * @param tradeCode 企业代码
     * @param userNo 用户账号
     * @param userName 用户名称
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int generateIOrderList(@Param("sid")String sid,
                            @Param("headSid") String headSid,
                            @Param("tradeCode") String tradeCode,
                            @Param("userNo") String userNo,
                            @Param("userName") String userName);


    /**
     * 判断是否已经生成 进货信息
     * @param sid 进口订单表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int getIsGeneratePurchase(@Param("sid") String sid);


    /**
     * 生成进货信息表头
     * @param sid 进口订单表头sid
     * @param userNo 用户账号
     * @param userName 用户名称
     * @param tradeCode 企业代码
     * @param purchaseHeadSid 进货信息表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int generatePurchaseHead(@Param("sid") String sid,
                             @Param("userNo") String userNo,
                             @Param("userName") String userName,
                             @Param("tradeCode") String tradeCode,
                             @Param("purchaseHeadSid")String purchaseHeadSid,
                             @Param("orderNo")String orderNo);



    /**
     * 生成进货信息表体
     * @param sid 进口订单表头sid
     * @param userNo 用户账号
     * @param userName 用户名称
     * @param tradeCode 企业代码
     * @param purchaseHeadSid 进货信息表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int generatePurchaseList(@Param("sid") String sid,
                             @Param("userNo") String userNo,
                             @Param("userName") String userName,
                             @Param("tradeCode") String tradeCode,
                             @Param("purchaseHeadSid")String purchaseHeadSid);


    /**
     * 校验多选合同号的供应商是否一致
     * @param sids 进口合同的sid
     * @return 返回供应商集合
     */
    List<String> getSuppliers(@Param("sids") List<String> sids);

    /**
     * 更新订单信息表头的进货单号
     * @param purchaseHeadSid 进货信息表头sid
     * @param sid 订单信息表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int updateOrderHeadPurchaseNo(@Param("purchaseHeadSid") String purchaseHeadSid, @Param("sid") String sid);


    /**
     * 获取计划编号(多个合同号进行拼接)
     * @param sids 进口合同的表头sid
     * @return 返回计划编号 多个计划编号拼接显示
     */
    String getPlanNo(@Param("sids") List<String> sids);


    /**
     * 获取订单表头汇总数据
     * @param bizIOrderHead 订单表头数据
     * @return 返回汇总数据
     */
    BizIOrderHeadTotal getOrderHeadTotal(BizIOrderHead bizIOrderHead);


    /**
     * 复制进口订单表头数据
     * @param copyVersionData 进口数据
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int copyVersion(CopyVersionData copyVersionData);


    /**
     * 作废数据
     * @param copyVersionData 作废数据请求参数（抄作废参数）
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int cancelData(CopyVersionData copyVersionData);


    /**
     * 级联删除 进口订单模块数据
     * @param sid 订单表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int deleteOrderAllData(@Param("orderHeadSid") String sid);



    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param sid 订单表头sid
     * @return 返回订单号集合
     */
    List<String> checkOrderNoNotCancel(@Param("orderHeadSid") String sid);


    /***
     * 进口订单作废时，更新进口费用表体的关联sid
     * @param sid 订单表体原Sid
     * @param newSid 订单表体新Sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int updateTurnoverSid(@Param("sid") String sid, @Param("newSid") String newSid);


    /**
     * 获取有效订单的sid集合
     * @param orderNo 订单号
     * @param tradeCode 企业编码
     * @return 返回有效订单sid集合
     */
    List<String> getValidOrdersSids(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);


    /**
     * 校验 进口费用表体是否存在  订单表体数据
     * @param sid 订单表头sid
     * @return  返回进口费用表体数据数量
     */
    int checkNextModuleExistEffectiveDataOrder(@Param("sid") String sid);


    /**
     * 校验 进货信息表体是否存在  订单表体数据
     * @param sid 订单表头sid
     * @return  返回进货信息表体数据数量
     */
    int checkNextModuleExistEffectiveDataPurchase(@Param("sid") String sid);


    /**
     * 获取当前企业的供应商信息
     * @param tradeCode 企业编码
     * @return 返回供应商信息集合 {label:供应商名称,value:供应商编码}
     */
    List<Map<String, String>> getOrderSupplierList(@Param("tradeCode") String tradeCode);


    /**
     * 根据业务主键获取随附件的文件信息
     * @param headId 业务主键 订单表头的sid
     * @return 返回附件信息集合
     */
    List<Attached> getAttachmentFile(@Param("headId") String headId);


    /**
     * 获取当前企业下 订单对应的版本号序号
     * @param orderNo 订单号
     * @return 返回版本号序号
     */
    String getVersionSerialNo(@Param("orderNo") String orderNo,@Param("tradeCode") String tradeCode);


    /**
     * 获取当前合同号的流水号
     * @param contractPrefix 截取后的合同号
     * @param tradeCode 企业编码
     * @return 返回流水号
     */
    CurrentContractSerialNo  getCurrentContractSerialNo(@Param("contractPrefix") String contractPrefix,@Param("tradeCode") String tradeCode);


    /**
     * 获取选择的合同号
     * @param contractNoSid 进口合同的Sid
     * @param tradeCode 企业代码
     * @return 返回合同号
     */
    String getContractNo(@Param("contractNoSid") String contractNoSid, @Param("tradeCode") String tradeCode);


    /**
     * 更新订单表头的删除标记
     * @param sid 订单表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int updateDeleteFlag(@Param("sid") String sid);


    /**
     * 根据截取合同号获取订单号
     * @param contractNo 截取后的合同号
     * @param tradeCode 企业编码
     * @return 返回订单号集合
     */
    List<String> getOrderNoList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


    /**
     * 校验当前企业订单号是否存在
     * @param orderNo 订单号
     * @param tradeCode 企业编码
     * @param sid 订单表头sid
     * @return
     */
    int checkOrderNo(@Param("orderNo") String orderNo,@Param("sid")String sid, @Param("tradeCode") String tradeCode);


    /**
     * 删除进货信息 装箱子表数据 等数据
     * @param sid 订单表头sid
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int deletePurchaseAll(@Param("sid") String sid);

    int getPurchaseCount(@Param("sid") String sid);

    int getReceiptHeadCount(@Param("sid") String sid);

    int getSellOrOutHeadCount(@Param("sid") String sid);

    String getPurchaseHeadIsNext(@Param("sid") String sid);

    String getSellOrOutHead(@Param("sid") String sid);

    int checkNextModuleExistPaymentDataOrder(@Param("headId") String headId);

    int checkNextModuleExistPaymentDataPurchase(@Param("headId")String headId);

    List<WorkFlowParam> getFlowList(List<String> ids);

    List<BizIOrderHead> getAeoList(BizIOrderHead bizIOrderHead);
}