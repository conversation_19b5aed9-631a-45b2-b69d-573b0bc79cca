<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizISellListMapper">

    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIncomingGoodsList">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="product_model" property="productModel" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="NUMERIC"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="delivery_date" property="deliveryDate" jdbcType="TIMESTAMP"/>
        <result column="total_usd" property="totalUsd" jdbcType="NUMERIC"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="in_quantity" property="inQuantity" jdbcType="NUMERIC"/>
        <result column="in_unit" property="inUnit" jdbcType="VARCHAR"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="contract_list_id" property="contractListId" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.sales_contract_number,
        t.sales_invoice_number,
        t.trade_name,
        t.unit,
        t.quantity,
        t.unit_price_excluding_tax,
        t.amount_of_tax,
        t.tax_not_included,
        t.total_value_tax,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code,
        t.I_COUNT,
        t.I_UNIT
    </sql>

    <sql id="condition">

    </sql>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizISellList" parameterType="com.dcjet.cs.dec.model.BizISellList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizISellList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getListBySids" resultType="com.dcjet.cs.dec.model.BizISellList">
        select * from T_BIZ_I_SELL_LIST t
        where t.sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSumDataByInvoiceSell" resultType="com.dcjet.cs.dec.model.BizISellList">
        select
               sum(amount_Of_Tax)       as amount_Of_Tax,
               sum(tax_Not_Included) as tax_Not_Included,
               sum(total_Value_Tax)  as total_Value_Tax,
               sum(I_COUNT)  as I_COUNT,
               sum(QUANTITY) as qtyTotal
        from T_BIZ_I_SELL_LIST
        where HEAD_ID = #{headId};
    </select>

    <select id="getSumDataByInvoiceSellSummary" resultType="com.dcjet.cs.dec.model.BizISellList">
        select sales_Invoice_Number           as sales_Invoice_Number,
               sum(amount_Of_Tax)       as amount_Of_Tax,
               sum(tax_Not_Included) as tax_Not_Included,
               sum(total_Value_Tax)  as total_Value_Tax
        from T_BIZ_I_SELL_LIST
        where HEAD_ID = #{headId}
        group by sales_Invoice_Number;
    </select>
    <select id="getTax" resultType="java.math.BigDecimal">
        select TAX_RATE from T_BIZ_MATERIAL_INFORMATION t where EXISTS (
            select 1 from T_BIZ_I_SELL_LIST g where g.TRADE_CODE = t.TRADE_CODE and g.TRADE_NAME = t.G_NAME
        )
        and t.TRADE_CODE = #{tradeCode} and t.G_NAME = #{tradeName} limit 1;
    </select>
    <select id="getPrice" resultType="java.math.BigDecimal">
        select price_excluding_tax from t_biz_quotation where G_NAME  = #{goodsName} and  specifications = #{productModel}  and status = '0' limit 1;
    </select>
    <select id="getListByHeadId" resultMap="BaseResultMap">
        select
            t.id,
            t.business_type,
            t.data_state,
            t.version_no,
            t.trade_code,
            t.sys_org_code,
            t.parent_id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.insert_user_name,
            t.update_user_name,
            t.extend1,
            t.extend2,
            t.extend3,
            t.extend4,
            t.extend5,
            t.extend6,
            t.extend7,
            t.extend8,
            t.extend9,
            t.extend10,
            t.goods_name,
            t.product_model,
            t.quantity,
            t.unit,
            t.unit_price,
            t.amount,
            t.delivery_date,
            t.total_usd,
            t.remarks,
            t.head_id,
            t.in_quantity,
            t.in_unit,
            t.curr,
            t.invoice_no,
            t.contract_list_id
            from  t_biz_incoming_goods_list t
        where
            t.head_id = #{headId}
    </select>
</mapper>