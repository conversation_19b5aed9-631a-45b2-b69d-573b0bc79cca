package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizIncomingGoodsDocumentMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsDocumentDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsDocument;

import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIncomingGoodsDocument业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-23 15:06:34
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsDocumentService extends BaseService<BizIncomingGoodsDocument> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsDocumentService.class);

    @Resource
    private BizIncomingGoodsDocumentMapper bizIncomingGoodsDocumentMapper;

    @Resource
    private BizIncomingGoodsDocumentDtoMapper bizIncomingGoodsDocumentDtoMapper;

    @Override
    public Mapper<BizIncomingGoodsDocument> getMapper() {
        return bizIncomingGoodsDocumentMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizIncomingGoodsDocumentParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsDocumentDto>> getListPaged(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsDocument bizIncomingGoodsDocument = bizIncomingGoodsDocumentDtoMapper.toPo(bizIncomingGoodsDocumentParam);
        bizIncomingGoodsDocument.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsDocument> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIncomingGoodsDocumentMapper.getList( bizIncomingGoodsDocument));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsDocumentDto> bizIncomingGoodsDocumentDtoList = page.getResult().stream()
            .map(bizIncomingGoodsDocumentDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIncomingGoodsDocumentDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIncomingGoodsDocumentParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsDocumentDto insert(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        BizIncomingGoodsDocument bizIncomingGoodsDocument = bizIncomingGoodsDocumentDtoMapper.toPo(bizIncomingGoodsDocumentParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIncomingGoodsDocument.setId(sid);
        bizIncomingGoodsDocument.setCreateBy(userInfo.getUserNo());
        bizIncomingGoodsDocument.setCreateTime(new Date());
        bizIncomingGoodsDocument.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIncomingGoodsDocumentMapper.insert(bizIncomingGoodsDocument);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIncomingGoodsDocumentDtoMapper.toDto(bizIncomingGoodsDocument) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIncomingGoodsDocumentParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsDocumentDto update(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        BizIncomingGoodsDocument bizIncomingGoodsDocument = bizIncomingGoodsDocumentMapper.selectByPrimaryKey(bizIncomingGoodsDocumentParam.getId());
        bizIncomingGoodsDocumentDtoMapper.updatePo(bizIncomingGoodsDocumentParam, bizIncomingGoodsDocument);
        bizIncomingGoodsDocument.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsDocument.setUpdateTime(new Date());

        // 更新数据
        int update = bizIncomingGoodsDocumentMapper.updateByPrimaryKey(bizIncomingGoodsDocument);
        return update > 0 ? bizIncomingGoodsDocumentDtoMapper.toDto(bizIncomingGoodsDocument) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIncomingGoodsDocumentMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsDocumentDto> selectAll(BizIncomingGoodsDocumentParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsDocument bizIncomingGoodsDocument = bizIncomingGoodsDocumentDtoMapper.toPo(exportParam);
        bizIncomingGoodsDocument.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsDocumentDto> bizIncomingGoodsDocumentDtos = new ArrayList<>();
        List<BizIncomingGoodsDocument> bizIncomingGoodsDocumentLists = bizIncomingGoodsDocumentMapper.getList(bizIncomingGoodsDocument);
        if (CollectionUtils.isNotEmpty(bizIncomingGoodsDocumentLists)) {
           bizIncomingGoodsDocumentDtos = bizIncomingGoodsDocumentLists.stream().map(head -> {
                    BizIncomingGoodsDocumentDto dto =  bizIncomingGoodsDocumentDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIncomingGoodsDocumentDtos;
    }


    /**
     * 功能描述:根据单据头ID查询单据明细
     * @param headId  单据头ID
     * @param userInfo 用户信息
     * @return 返回单据明细列表
     */
    public ResultObject<BizIncomingGoodsDocumentDto> getDocumentByHeadId(String headId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        Assert.hasText(headId, "进货管理表头ID不能为空！");
        BizIncomingGoodsDocument documentByHeadId = bizIncomingGoodsDocumentMapper.getDocumentByHeadId(headId);
        if (documentByHeadId != null) {
            BizIncomingGoodsDocumentDto dto = bizIncomingGoodsDocumentDtoMapper.toDto(documentByHeadId);
            resultObject.setData(dto);
        }
        return resultObject;
    }


    /**
     * 更新数据，如果存在则更新，不存在则新增
     * @param bizIncomingGoodsDocumentParam 进货信息-证件信息
     * @param userInfo 用户信息
     * @return 返回更新后的DTO对象
     */
    public BizIncomingGoodsDocumentDto insertOrUpdate(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "更新成功！");
        BizIncomingGoodsDocument po = bizIncomingGoodsDocumentDtoMapper.toPo(bizIncomingGoodsDocumentParam);
        // 判断ID是否存在，如果存在则更新，不存在则新增
        if (StringUtils.isBlank(po.getId())){
            // 新增数据
            po.setCreateBy(userInfo.getUserNo());
            po.setCreateTime(new Date());
            po.setTradeCode(userInfo.getCompany());
            po.setId(UUID.randomUUID().toString());
            bizIncomingGoodsDocumentMapper.insert(po);
        }else{
            // 更新数据
            po.setUpdateBy(userInfo.getUserNo());
            po.setUpdateTime(new Date());
            bizIncomingGoodsDocumentMapper.updateByPrimaryKey(po);
        }
        // 将PO转为DTO返回给前端
        return bizIncomingGoodsDocumentDtoMapper.toDto(po);
    }
}