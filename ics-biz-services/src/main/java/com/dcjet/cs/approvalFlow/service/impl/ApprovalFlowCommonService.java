package com.dcjet.cs.approvalFlow.service.impl;

import com.dcjet.cs.approvalFlow.factory.ApprovalFlowFactory;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ApprovalFlowCommonService {

    @Resource
    private ApprovalFlowFactory approvalFlowFactory;

    @Resource
    private CommonService commonService;

    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendAudit(ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) throws InterruptedException {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审核成功"));
        // 调用发送审核 审批流
        // 解析返回信息
        NextNodeInfoBatchVo batchVo = commonService.startFlowBatch(approvalFlowParam.getBusinessType(), approvalFlowParam.getBillType(), approvalFlowParam.getIds(), userInfo);
        String type = approvalFlowParam.getBusinessType() + "_" + approvalFlowParam.getBillType();
        ApprovalFlowService service = approvalFlowFactory.getService(CommonEnum.APPROVAL_FLOW_BUSSINESS_ENUM.getValue(type));
        //对应业务类型处理
        service.startFlowBatch(batchVo, approvalFlowParam, userInfo);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject audit(ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) throws InterruptedException {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核成功"));

        String type = approvalFlowParam.getBusinessType() + "_" + approvalFlowParam.getBillType();
        ApprovalFlowService service = approvalFlowFactory.getService(CommonEnum.APPROVAL_FLOW_BUSSINESS_ENUM.getValue(type));

        List<WorkFlowParam> flows = service.getFlowList(approvalFlowParam.getIds());
        Map<String, String> flowInstanceMap = flows.stream().collect(Collectors.toMap(WorkFlowParam::getFlowInstanceId, WorkFlowParam::getSid));
        // 调用 审批通过-审批流
        // 解析 返回结果
        List<NextNodeInfoVo> nextNodeInfoVos = commonService.passBatch(flows, approvalFlowParam.getApprMessage(), userInfo);

        //对应业务类型处理
        service.audit(nextNodeInfoVos, approvalFlowParam, flowInstanceMap, userInfo);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject reject(ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核退回成功"));

        String type = approvalFlowParam.getBusinessType() + "_" + approvalFlowParam.getBillType();
        ApprovalFlowService service = approvalFlowFactory.getService(CommonEnum.APPROVAL_FLOW_BUSSINESS_ENUM.getValue(type));

        List<WorkFlowParam> flows = service.getFlowList(approvalFlowParam.getIds());
        // 审核退回 对接审批流
        // 解析 返回结果
        List<NextNodeInfoVo> nextNodeInfoVos = commonService.rejectBatch(flows, approvalFlowParam.getApprMessage(), userInfo);
        service.reject(nextNodeInfoVos, approvalFlowParam, userInfo);

        return result;
    }
}
