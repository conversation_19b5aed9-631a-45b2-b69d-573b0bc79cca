package com.dcjet.cs.approvalFlow.service;

import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.xdo.common.token.UserInfoToken;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;

import java.util.List;
import java.util.Map;

public interface ApprovalFlowService {

    /**
     * 发送审核
     * @param batchVo
     */
    void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo);

    /**
     * 审核
     * @param nextNodeInfoVos
     * @param approvalFlowParam
     */
    void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo);

    /**
     * 审核退回
     * @param nextNodeInfoVos
     * @param approvalFlowParam
     */
    void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo);

    /**
     * 获取业务主键及审批流实例ID
     * @param ids
     * @return
     */
    List<WorkFlowParam> getFlowList(List<String> ids);
}
