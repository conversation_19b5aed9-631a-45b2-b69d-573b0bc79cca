package com.dcjet.cs.attach.dao;


import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.dto.attach.AttachedDto;
import com.xdo.domain.KeyValuePair;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* Attached
* <AUTHOR>
* @date: 2019-4-25
*/
public interface AttachedMapper extends Mapper<Attached> {
    /**
     * 查询获取数据
     * @param attached
     * @return
     */
    List<Attached> getList(Attached attached);
    List<String> getSidList(@Param("tradeCode") String tradeCode, @Param("billSids") List<String> billSids);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     *
     * 功能描述:
     *
     * @auther zhuhui
     * @version    1.0
     * @date  20190307
     * @param headId 提单表头id
     * @return
     */
    List<KeyValuePair<String,String>> getIPreAcmpInfo(String headId);
    /**
     *
     * 功能描述:
     *
     * @auther zhuhui
     * @version    1.0
     * @date  20190307
     * @param headId 提单表头id
     * @return
     */
    List<KeyValuePair<String, String>> getEPreAcmpInfo(String headId);



    List<Attached> selectData(@Param("businessSid") String businessSid, @Param("fileName") String fileName);






    List<Attached> getAttachedByHeadId(@Param("headId") String headId,@Param("businessType")String businessType);


    int getCustomsDocCount(String erpHeadId);

    List<Attached> getByHeadId(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    /**
     * 根据进货单号查询附件信息
     * @param purchaseOrderNo 进货单号
     * @param tradeCode 企业编码
     * @return 附件列表
     */
    List<Attached> getByPurchaseOrderNo(@Param("purchaseOrderNo") String purchaseOrderNo, @Param("tradeCode") String tradeCode);
}
