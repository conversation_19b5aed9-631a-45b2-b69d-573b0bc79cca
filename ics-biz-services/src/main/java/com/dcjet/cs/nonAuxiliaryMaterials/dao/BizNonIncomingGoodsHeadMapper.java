package com.dcjet.cs.nonAuxiliaryMaterials.dao;


import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;

import com.dcjet.cs.dto.dec.BizListExtractContractList;
import com.dcjet.cs.dto.dec.GenerateTBFileData;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHeadList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 进货管理-表头数据Mapper
 */
public interface BizNonIncomingGoodsHeadMapper extends Mapper<BizNonIncomingGoodsHead>{

    /**
     * 查询获取数据
     * @param tBizNonIncomingGoodsHead
     * @return
     */
    List<BizNonIncomingGoodsHead> getList(BizNonIncomingGoodsHead tBizNonIncomingGoodsHead);
    List<BizNonIncomingGoodsHead> getListToStore(BizNonIncomingGoodsHead tBizNonIncomingGoodsHead);
    List<BizNonIncomingGoodsHead> getListBySids(List<String> sids);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);



    /**
     * 进货管理-获取供应商列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getOrderSupplierList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取港口列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPortList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取币别列表信息
     * @param
     * @return 返回结果
     */
    List<Map<String, String>> getCurrList(BizNonIncomingGoodsHeadParam params);


    /**
     * 进货管理-获取价格条款
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPriceTermList(@Param("tradeCode") String tradeCode);

    String getPriceTermsByCode(@Param("tradeCode") String tradeCode,@Param("code") String code);

    /** 获取单位列表
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getUnitList(@Param("tradeCode") String tradeCode);

    List<BizIAuxmatForContractHead> getExtractContractInfo(@Param("contractNo")String contractNo,@Param("tradeCode") String tradeCode);

    int getAuxmatForContractNumber(@Param("id")String id,@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    List<BizListExtractContractList> getContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);
    List<BizListExtractContractList> getContractList2(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);
    BigDecimal getCount(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);

    Integer getCurrentContractNoMaxSerial(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


    BigDecimal getAuxmatCurrentContractNoSum(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);
    BigDecimal getIncomingGoodsHeadSum(@Param("contractNo") String contractNo, @Param("goodsName") String goodsName, @Param("tradeCode") String tradeCode);

    List<BizNonIncomingGoodsHeadList> getListPagedNew(BizNonIncomingGoodsHead tBizNonIncomingGoodsHead);


    int deleteDocumentByHeadId(@Param("sid") String sid);


    List<Map<String,String>> getOrderSupplierListDistinct(@Param("tradeCode") String tradeCode);

    String getPriceTerm(@Param("id") String id);

    Integer selectByPurchaseNo(@Param("purchaseNo") String purchaseNo, @Param("id") String id, @Param("tradeCode") String tradeCode);

    Integer deleteSellListByHeadId(@Param("sid") String sid);

    Integer deleteSellHeadByHeadId(@Param("sid") String sid);

    int cancelData(@Param("id") String id);

    int returnOrder(@Param("id") String id);

    Integer checkIsGenerate(@Param("id") String id);


    String getCigarettePaper(@Param("id") String id);

    GenerateTBFileData getSumTotal(@Param("id") String id);

    List<BizIAuxmatForContractHead> getExtractAuxmatContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    BigDecimal getIncomingCountByContract(@Param("contractNo") String contractNoTemp, @Param("tradeCode") String tradeCode);

    List<BizNonIncomingGoodsHead> getListPagedToCustomerAccount(BizNonIncomingGoodsHead tBizNonIncomingGoodsHead);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid);


}