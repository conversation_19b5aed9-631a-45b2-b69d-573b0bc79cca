<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="DATA_STATE" property="dataState" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
        <result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR"/>
        <result column="PURCHASE_NO" property="purchaseNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER" property="customer" jdbcType="VARCHAR"/>
        <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="PORT_OF_DEPARTURE" property="portOfDeparture" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR"/>
        <result column="VESSEL_VOYAGE" property="vesselVoyage" jdbcType="VARCHAR"/>
        <result column="SAILING_DATE" property="sailingDate" jdbcType="TIMESTAMP"/>
        <result column="SALES_DATE" property="salesDate" jdbcType="TIMESTAMP"/>

        <result column="DOCUMENT_CREATOR" property="documentCreator" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_DATE" property="documentDate" jdbcType="TIMESTAMP"/>
        <result column="DOCUMENT_STATUS" property="documentStatus" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPROVAL_STATUS" property="approvalStatus" jdbcType="VARCHAR"/>

        <result column="IS_NEXT" property="isNext" jdbcType="TIMESTAMP"/>
        <result column="ENTRY_NO" property="entryNo" jdbcType="VARCHAR"/>
        <result column="SEND_ENTRY" property="sendEntry" jdbcType="VARCHAR"/>
        <result column="ENTRY_DATE" property="entryDate" jdbcType="TIMESTAMP"/>
        <result column="non_incoming_invoice_no" property="nonIncomingInvoiceNo" jdbcType="TIMESTAMP"/>

        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
<!--        <result column="SELL_CONTRACT_NO" property="sellContractNo" jdbcType="VARCHAR"/>-->
    </resultMap>

    <sql id="Base_Column_List">
            t.ID, 
            t.BUSINESS_TYPE, 
            t.DATA_STATE, 
            t.VERSION_NO, 
            t.TRADE_CODE, 
            t.SYS_ORG_CODE, 
            t.PARENT_ID, 
            t.CREATE_BY, 
            t.CREATE_TIME, 
            t.UPDATE_BY, 
            t.UPDATE_TIME, 
            t.INSERT_USER_NAME, 
            t.UPDATE_USER_NAME, 
            t.EXTEND1, 
            t.EXTEND2, 
            t.EXTEND3, 
            t.EXTEND4, 
            t.EXTEND5, 
            t.EXTEND6, 
            t.EXTEND7, 
            t.EXTEND8, 
            t.EXTEND9, 
            t.EXTEND10, 
            t.CONTRACT_NO, 
            t.PURCHASE_NO, 
            t.CURR,
            t.CUSTOMER,
            t.SUPPLIER, 
            t.INVOICE_NO, 
            t.PORT_OF_DEPARTURE, 
            t.DESTINATION,
            t.PRICE_TERM, 
            t.PRICE_TERM_PORT, 
            t.VESSEL_VOYAGE, 
            t.SAILING_DATE, 
            t.SALES_DATE,
            t.DOCUMENT_CREATOR, 
            t.DOCUMENT_DATE, 
            t.DOCUMENT_STATUS, 
            t.CONFIRM_TIME, 
            t.APPROVAL_STATUS, 
            t.IS_NEXT,
            t.ENTRY_NO,
            t.ENTRY_DATE,
            t.SEND_ENTRY,
            t.non_incoming_invoice_no,
            t.NOTE,
               (select sum(AMOUNT) from T_BIZ_NON_INCOMING_GOODS_LIST where HEAD_ID = t.ID)     totalAmount
    </sql>

    <sql id="condition">
        <if test="createBy != null and createBy  != ''">
            AND t.CREATE_BY LIKE '%' || #{createBy} || '%'
        </if>
        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and t.CREATE_TIME >= to_timestamp(#{createTime},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and t.CREATE_TIME < to_timestamp(#{createTime},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="contractNo != null and contractNo  != ''">
            AND t.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        <if test="purchaseNo != null and purchaseNo  != ''">
            AND t.PURCHASE_NO LIKE '%' || #{purchaseNo} || '%'
        </if>
        <if test="customer != null and customer  != ''">
            AND t.CUSTOMER LIKE '%' || #{customer} || '%'
        </if>
        <if test="supplier != null and supplier  != ''">
            AND t.SUPPLIER LIKE '%' || #{supplier} || '%'
        </if>



    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            T_BIZ_NON_INCOMING_GOODS_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getListToStore" resultMap="BaseResultMap" parameterType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM T_BIZ_NON_INCOMING_GOODS_HEAD t
        WHERE NOT EXISTS (
        SELECT 1
        FROM T_BIZ_STORE_I_HEAD t2
        WHERE t2.status != '2' and t.PURCHASE_NO IN (
        SELECT TRIM(REGEXP_SUBSTR(t2.PURCHASE_NO_MARK, '[^;]+', 1, LEVEL))
        FROM dual
        CONNECT BY REGEXP_SUBSTR(t2.PURCHASE_NO_MARK, '[^;]+', 1, LEVEL) IS NOT NULL
        )
        )
        <if test="purchaseNo != null and purchaseNo  != ''">
            AND t.PURCHASE_NO LIKE '%' || #{purchaseNo} || '%'
        </if>
        <if test="tradeCode != null and tradeCode  != ''">
            AND t.TRADE_CODE = #{tradeCode}
        </if>
        <if test="dataState != null and dataState  != ''">
            AND t.DATA_STATE = #{dataState}
        </if>
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            merchant_code as "value",
            merchant_name_cn as "label"
        from  T_BIZ_MERCHANT
        where TRADE_CODE = #{tradeCode};
    </select>
    <select id="getPortList" resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'PORT'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getCurrList" resultType="java.util.Map">
        select
            distinct
            custom_param_code as "value",
            params_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'CURR'
        <if test="businessType != null and businessType  != ''">
            and BUSINESS_TYPE like '%' || #{businessType} || '%'
        </if>
          and TRADE_CODE = #{tradeCode}
    </select>

    <select id="getUnitList" resultType="java.util.Map">
        select
            distinct
            PARAMS_CODE as "value",
            params_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'UNIT'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getPriceTermList" resultType="java.util.Map">
        select param_code as "value",
               price_term as "label"
        from T_BIZ_PRICE_TERMS
        where TRADE_CODE = #{tradeCode};
    </select>
    <select id="getListBySids" resultType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_NON_INCOMING_GOODS_HEAD t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getExtractContractInfo"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead">
        SELECT DISTINCT
            h.ID,
            h.CONTRACT_NO,
            h.CUSTOMER_NAME,
            h.SIGN_DATE,
            h.TRADE_CODE,
            h.SUPPLIER_NAME || '  ' ||tbm.MERCHANT_NAME_CN AS SUPPLIER_NAME
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
        LEFT JOIN  T_BIZ_MERCHANT tbm on h.SUPPLIER_NAME  = tbm.MERCHANT_CODE  AND h.TRADE_CODE  = tbm.TRADE_CODE
        WHERE h.STATUS = '1'
          AND EXISTS (
            SELECT 1
            FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST cl
            WHERE cl.HEAD_ID = h.ID
              AND cl.QTY > COALESCE((
                SELECT SUM(il.QUANTITY)
                FROM T_BIZ_NON_INCOMING_GOODS_HEAD ih
                         INNER JOIN T_BIZ_NON_INCOMING_GOODS_LIST il ON ih.ID = il.HEAD_ID
                WHERE ih.CONTRACT_NO = h.CONTRACT_NO
                  AND ih.TRADE_CODE = h.TRADE_CODE
                  AND ih.DATA_STATE != 2
              AND il.DATA_STATE != 2
          ), 0)
        )

        <if test="contractNo != null and contractNo != ''">
            AND h.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        ORDER BY h.SIGN_DATE DESC, h.CONTRACT_NO;

    </select>



    <select id="getAuxmatForContractNumber" resultType="java.lang.Integer">
        select
            COALESCE(sum(l.QTY),0)
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l
                 left join T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h on l.HEAD_ID = h.ID
        where h.CONTRACT_NO = #{contractNo} and h.TRADE_CODE = #{tradeCode}  and (h.id  = #{id} or h.STATUS = '1')
    </select>

    <!-- 获取进货信息表体数量 -->
    <select id="getContractList" resultType="com.dcjet.cs.dto.dec.BizListExtractContractList">
        SELECT
            ih.CONTRACT_NO as "contractNo",
            ih.TRADE_CODE as "tradeCode",
            ih.SUPPLIER   as "supplierName",
            il.GOODS_NAME as "goodsName",
            SUM(il.QUANTITY) as "qty",
            il.head_id as "headId"
        FROM  T_BIZ_NON_INCOMING_GOODS_LIST il
                  LEFT JOIN T_BIZ_NON_INCOMING_GOODS_HEAD ih ON ih.ID = il.HEAD_ID
        WHERE ih.DATA_STATE != 2
        <if test="contractNo != null and contractNo != ''">
            AND ih.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        GROUP BY ih.CONTRACT_NO, ih.TRADE_CODE,il.GOODS_NAME,ih.SUPPLIER_NAME
    </select>


    <!-- 获取外商合同表体数量 -->
    <select id="getContractList2" resultType="com.dcjet.cs.dto.dec.BizListExtractContractList">
        SELECT
            ch.CONTRACT_NO as "contractNo",
            ch.TRADE_CODE as "tradeCode",
            ch.SUPPLIER_NAME as "supplierName",
            cl.GOODS_NAME AS "productName",
            cl.head_id as "headId",
            SUM(cl.QTY) as "qty"
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST cl
                 LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD ch  ON ch.ID = cl.HEAD_ID
        WHERE
            ch.STATUS  = '1'
        <if test="contractNo != null and contractNo != ''">
            AND ch.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        GROUP BY ch.CONTRACT_NO, ch.TRADE_CODE,cl.GOODS_NAME,cl.head_id,ch.SUPPLIER_NAME
    </select>
    <select id="getCount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(il.QUANTITY),0)
        FROM  T_BIZ_NON_INCOMING_GOODS_LIST il
        LEFT JOIN T_BIZ_NON_INCOMING_GOODS_HEAD ih ON ih.ID = il.HEAD_ID
        WHERE ih.DATA_STATE != 2  and ih.contract_no = #{contractNo} and ih.trade_code = #{tradeCode} and il.GOODS_NAME = #{goodsName}
        GROUP BY ih.CONTRACT_NO, ih.TRADE_CODE,il.GOODS_NAME
    </select>
    <select id="getCurrentContractNoMaxSerial" resultType="java.lang.Integer">
        select MAX(RIGHT(PURCHASE_NO, 2)::int)  as serialNo
        from T_BIZ_NON_INCOMING_GOODS_HEAD where  CONTRACT_NO = #{contractNo} and TRADE_CODE = #{tradeCode} and REGEXP_LIKE(PURCHASE_NO, '\d{2}$');
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  T_BIZ_NON_INCOMING_GOODS_HEAD t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteDocumentByHeadId">
        delete from T_BIZ_INCOMING_GOODS_DOCUMENT where PARENT_ID = #{sid}
    </delete>
    <delete id="deleteSellListByHeadId">
        delete from T_BIZ_I_SELL_LIST l where HEAD_ID in (
            select sid from T_BIZ_I_SELL_HEAD  h where h.SID = l.HEAD_ID and h.HEAD_ID = #{sid}
        )
    </delete>
    <delete id="deleteSellHeadByHeadId">
        delete from T_BIZ_I_SELL_HEAD h where h.HEAD_ID  = #{sid}
    </delete>


    <update id="cancelData">
        update T_BIZ_NON_INCOMING_GOODS_HEAD set  DATA_STATE = '2' where ID = #{id};
        update T_BIZ_I_SELL_HEAD set SALES_DOCUMENT_STATUS = '2' where HEAD_ID = #{id};
        update T_BIZ_INCOMING_GOODS_DOCUMENT set DATA_STATE = '2' where HEAD_ID = #{id};
    </update>

    <update id="returnOrder">
        update T_BIZ_I_SELL_HEAD set SALES_DOCUMENT_STATUS = '0' where HEAD_ID = #{id};
    </update>
    <update id="updateCorrelationID">
        update T_BIZ_NON_INCOMING_GOODS_LIST set
            contract_list_id = #{newListId} where contract_list_id = #{oldSid};
    </update>


    <select id="getAuxmatCurrentContractNoSum" resultType="java.math.BigDecimal">
        select COALESCE(SUM(l.QTY),0)
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l
        LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h ON l.HEAD_ID = h.ID
        WHERE h.CONTRACT_NO = #{contractNo} and h.TRADE_CODE = #{tradeCode} and l.GOODS_NAME = #{goodsName} and h.STATUS = '1'
    </select>

    <select id="getIncomingGoodsHeadSum" resultType="java.math.BigDecimal">
        select COALESCE(SUM(l.QUANTITY),0)
        from T_BIZ_NON_INCOMING_GOODS_LIST l
        LEFT JOIN T_BIZ_NON_INCOMING_GOODS_HEAD h ON l.HEAD_ID = h.ID
        WHERE h.CONTRACT_NO = #{contractNo} and h.TRADE_CODE = #{tradeCode} and l.GOODS_NAME = #{goodsName} and h.DATA_STATE != 2
    </select>
    <select id="getListPagedNew" parameterType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead" resultType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHeadList">
        WITH list_temp AS (
            SELECT
                l.HEAD_ID,
                LISTAGG(DISTINCT l.INVOICE_NO, ',') WITHIN GROUP (ORDER BY l.INVOICE_NO) AS INVOICE_NO
        FROM T_BIZ_NON_INCOMING_GOODS_LIST l
        WHERE COALESCE(l.INVOICE_NO, '') != ''
          AND l.DATA_STATE != '2'
        GROUP BY l.HEAD_ID
            ),
        sell_temp AS (
            SELECT
                distinct
                h.HEAD_ID,
                h.SALES_DOCUMENT_STATUS as sell_status,
                LISTAGG(DISTINCT l.SALES_INVOICE_NUMBER, ',') WITHIN GROUP (ORDER BY l.SALES_INVOICE_NUMBER) AS sell_invoice_no
            FROM
            T_BIZ_I_SELL_LIST l
            left join T_BIZ_I_SELL_HEAD h on l.HEAD_ID = h.sid

                GROUP BY
                h.HEAD_ID,
                h.SALES_DOCUMENT_STATUS
        )
        SELECT
            h.ID as id,
            h.PURCHASE_NO as purchaseNo,
            h.SUPPLIER  || ' ' || m.MERCHANT_NAME_CN as supplier,
            COALESCE(lt.INVOICE_NO, '') as invoiceNo,
            COALESCE(d.LICENSE_NUMBER, '') as licenseNumber,
            COALESCE(d.PERMIT_NUMBER, '') as permitNumber,
            COALESCE(st.sell_invoice_no,'') as sellInvoiceNo,
            COALESCE(st.sell_status,'-1') as sellStatus,
            h.DATA_STATE as dataState,
            COALESCE(h.UPDATE_USER_NAME, h.INSERT_USER_NAME) as createBy,
            COALESCE(h.UPDATE_TIME, h.CREATE_TIME) as createTime,
            h.is_next as isNext
        FROM T_BIZ_NON_INCOMING_GOODS_HEAD h
                 LEFT JOIN list_temp lt ON lt.HEAD_ID = h.ID
                 LEFT JOIN T_BIZ_INCOMING_GOODS_DOCUMENT d ON h.ID = d.PARENT_ID
                 LEFT JOIN T_BIZ_MERCHANT m ON m.TRADE_CODE = h.TRADE_CODE AND h.SUPPLIER = m.MERCHANT_CODE
                 left join sell_temp st on st.HEAD_ID = h.ID
            <where>
                 <if test="tradeCode != null and tradeCode != ''">
               AND h.TRADE_CODE = #{tradeCode}
                 </if>

                  <choose>
                      <when test="dataState != null and dataState != ''">
                      AND h.DATA_STATE = #{dataState}
                      </when>
                      <otherwise>
                      AND h.DATA_STATE != '2'
                      </otherwise>
                  </choose>
                 <if test="purchaseNo != null and purchaseNo != ''">
               AND h.PURCHASE_NO LIKE CONCAT('%', #{purchaseNo}, '%')
                 </if>
                 <if test="supplier != null and supplier != ''">
               AND h.SUPPLIER = #{supplier}
                 </if>

                <if test="createTimeFrom != null and createTimeFrom != ''">
                    <![CDATA[  AND COALESCE(h.UPDATE_TIME, h.CREATE_TIME) >= TO_DATE(#{createTimeFrom,jdbcType=VARCHAR}, 'yyyy-mm-dd')]]>
                </if>
                <if test="createTimeTo != null and createTimeTo != ''">
                    <![CDATA[ AND COALESCE(h.UPDATE_TIME, h.CREATE_TIME) <  TO_DATE(#{createTimeTo,jdbcType=VARCHAR}, 'yyyy-mm-dd') + 1 ]]>
                </if>
                <if test="sellStatus != null and sellStatus != ''">
                    AND st.sell_status = #{sellStatus}
                </if>
        </where>
        ORDER BY COALESCE(h.UPDATE_TIME, h.CREATE_TIME) DESC

    </select>
    <select id="getOrderSupplierListDistinct" resultType="java.util.Map">
        select
            DISTINCT
                h.SUPPLIER as "value",
                m.MERCHANT_NAME_CN as "label"
            from T_BIZ_NON_INCOMING_GOODS_HEAD h
                left join T_BIZ_MERCHANT m on m.TRADE_CODE = h.TRADE_CODE and h.SUPPLIER = m.MERCHANT_CODE
            where  COALESCE(h.SUPPLIER,'') != ''
            <if test="tradeCode!= null and tradeCode!= ''">
                AND h.TRADE_CODE = #{tradeCode}
            </if>

    </select>
    <select id="getPriceTerm" resultType="java.lang.String">
        select
            PRICE_TERM
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD l

        where l.id  =  #{id}
    </select>
    <select id="selectByPurchaseNo" resultType="java.lang.Integer">
        select count(1)
        from T_BIZ_NON_INCOMING_GOODS_HEAD h
        where h.PURCHASE_NO = #{purchaseNo}
          and h.TRADE_CODE = #{tradeCode}
          and h.DATA_STATE != 2
          <if test="id != null and id != ''">
              AND h.ID != #{id}
          </if>
    </select>


    <select id="checkIsGenerate" resultType="java.lang.Integer">
        select count(1) from  T_BIZ_I_SELL_HEAD  where HEAD_ID = #{id};
    </select>
    <select id="getCigarettePaper" resultType="java.lang.String">
        select FULL_EN_NAME from T_BIZ_NON_INCOMING_GOODS_LIST l
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME  and l.TRADE_CODE = m.TRADE_CODE
        where COALESCE(l.GOODS_NAME,'') != '' and l.head_id = #{id}  limit 1 ;
    </select>
    <select id="getSumTotal" resultType="com.dcjet.cs.dto.dec.GenerateTBFileData">
        select
            sum(QUANTITY) as totalQuantity,
            sum(AMOUNT) as totalAmount
        from T_BIZ_NON_INCOMING_GOODS_LIST
        where HEAD_ID = #{id}
    </select>
    <select id="getExtractAuxmatContractList"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead">
        SELECT DISTINCT
        h.ID,
        h.contract_no,
        h.SUPPLIER CUSTOMER_NAME,
        h.SIGNING_DATE SIGN_DATE,
        h.TRADE_CODE,
        (select sum(QTY) from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l where l.HEAD_ID = h.id) as totalQuantity,
        h.SUPPLIER || '  ' ||tbm.MERCHANT_NAME_CN AS SUPPLIER_NAME
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
                 LEFT JOIN  T_BIZ_MERCHANT tbm on h.SUPPLIER  = tbm.MERCHANT_CODE  AND h.TRADE_CODE  = tbm.TRADE_CODE
        WHERE h.STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
        AND h.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        ORDER BY h.SIGNING_DATE DESC, h.CONTRACT_NO

    </select>
    <select id="getIncomingCountByContract" resultType="java.math.BigDecimal">
        SELECT SUM(il.QUANTITY)
        FROM T_BIZ_NON_INCOMING_GOODS_HEAD ih
                 INNER JOIN T_BIZ_NON_INCOMING_GOODS_LIST il ON ih.ID = il.HEAD_ID
        WHERE ih.CONTRACT_NO = #{contractNo}
          AND ih.TRADE_CODE = #{tradeCode}
          AND ih.DATA_STATE != 2
              AND il.DATA_STATE != 2
    </select>
    <select id="getPriceTermsByCode" resultType="java.lang.String">
        select price_term
        from T_BIZ_PRICE_TERMS
        where TRADE_CODE = #{tradeCode} and param_code=#{code};
    </select>
    <select id="getListPagedToCustomerAccount"
            resultType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead">
        SELECT t.id,t.purchase_no,t.contract_no,max(th.DOMESTIC_PRINCIPAL) as customer ,t.curr,
        sum(tl.amount) AS decTotalToList, sum(tl.quantity) AS qtyToList, max(tl.unit) AS unitToList, max(tm.MERCHANDISE_CATEGORIES) AS merchandiseCategoriesToList
        FROM
        T_BIZ_NON_INCOMING_GOODS_HEAD t
        LEFT JOIN T_BIZ_NON_INCOMING_GOODS_LIST tl ON t.id = tl.head_id
        LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD th ON th.contract_no = t.contract_no
        LEFT JOIN T_BIZ_MATERIAL_INFORMATION tm ON tm.g_name = tl.goods_name
        WHERE NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE tc.CONTRACT_NO = t.contract_no AND tc.STATUS != '2' )
            AND t.DOCUMENT_STATUS = '1'
            <if test="contractNo != null and contractNo != ''">
                and t.CONTRACT_NO like '%'|| #{contractNo} || '%'
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                and t.purchase_no like '%'|| #{purchaseNo} || '%'
            </if>
            <if test="businessType != null and businessType != ''">
                and t.BUSINESS_TYPE = #{businessType}
            </if>
        GROUP BY t.id,t.purchase_no,t.contract_no,t.curr
    </select>


</mapper>