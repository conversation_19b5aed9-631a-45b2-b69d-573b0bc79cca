package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIOrderHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:18
*/
@RestController
@RequestMapping("v1/bizIOrderHead")
@Api(tags = "进口管理-订单信息表头接口")
public class BizIOrderHeadController{

    @Resource
    private BizIOrderHeadService bizIOrderHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
    @Resource
    private ExportService exportService;

    /**
     * 分页获取进口管理-订单信息表头数据
     * @param bizIOrderHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-订单信息表头数据")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/importedCigarettes/order")
    public ResultObject<List<BizIOrderHeadDto>> getListPaged(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIOrderHeadDto>> paged = bizIOrderHeadService.getListPaged(bizIOrderHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("分页获取进口管理-订单信息表头数据")
    @PostMapping("aeoList")
    public ResultObject<List<BizIOrderHeadDto>> getAeoListPaged(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIOrderHeadDto>> paged = bizIOrderHeadService.getAeoListPaged(bizIOrderHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIOrderHeadDto> insert(@Valid @RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIOrderHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIOrderHeadDto bizIOrderHeadDto = bizIOrderHeadService.insert(bizIOrderHeadParam, userInfo);
        if (bizIOrderHeadDto != null) {
            resultObject.setData(bizIOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIOrderHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        bizIOrderHeadParam.setSid(sid);
        BizIOrderHeadDto bizIOrderHeadDto = bizIOrderHeadService.update(bizIOrderHeadParam,bizIOrderHeadParam.getOrderListParams(), userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIOrderHeadDto != null) {
            resultObject.setData(bizIOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIOrderHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIOrderHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIOrderHeadDto> bizIOrderHeadDtos = bizIOrderHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIOrderHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIOrderHeadDtos);
    }


    /**
     * 获取进口合同数据
     */
    @ApiOperation("获取进口合同数据")
    @PostMapping("/getIContractList")
    public ResultObject<List<BizIOrderExtractDto>> getIContractList(@RequestBody BizIOrderExtractParams params,PageParam pageParam ,UserInfoToken userInfo) {
        return bizIOrderHeadService.getIContractList(params,pageParam,userInfo);
    }


    /**
     * 需求变更 订单号-需要可以作废单据号
     * @param params 订单表头信息
     * @param userInfo 用户信息
     * @return 返回结果  [ rebootOrderNo:['订单号1','订单号2'],minOrderNo:'订单号1']
     */
    @ApiOperation("重启订单号弹框")
    @PostMapping("/rebootOrderNo")
    public ResultObject rebootOrderNo(@RequestBody BizIOrderExtractParams params, UserInfoToken userInfo) {
        ResultObject resultObject = bizIOrderHeadService.rebootOrderNo(params, userInfo);
        return resultObject;
    }

    /**
     * 生成进口订单数据（将进口合同符合条数据加载到进口管理）
     * @param params  请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("生成进口订单数据（将进口合同符合条数据加载到进口管理）")
    @PostMapping("/generateIOrder")
    public ResultObject generateIOrder(@RequestBody BizIOrderExtractParams params, UserInfoToken userInfo) {
        // 表头sid
        String headSid = UUID.randomUUID().toString();
        ResultObject resultObject = bizIOrderHeadService.generateIOrder(headSid,params, userInfo);
        return resultObject;
    }


    /**
     * 订单管理表头-确认
     * @param params 订单表头信息
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("订单管理表头-确认")
    @PostMapping("/confirmIOrderHead")
    public ResultObject confirmIOrderHead(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.confirmIOrderHead(params,userInfo);
    }


    /**
     * 获取订单表头的汇总的数据
     * @param params 订单表头信息
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("获取订单表头汇总数据")
    @PostMapping("/getOrderHeadTotal")
    public ResultObject getOrderHeadTotal(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.getOrderHeadTotal(params,userInfo);
    }


    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("校验是否存在 同一个订单号是否存在未作废的数据")
    @PostMapping("/checkOrderNoNotCancel")
    public ResultObject checkOrderNoNotCancel(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.checkOrderNoNotCancel(params,userInfo);
    }


    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.copyVersion(params,userInfo);
    }


    /**
     * 校验下游模块是否存在有效数据
     * 订单表头（N） -> 进口费用 (1)
     * @param params 请求参数（订单表头SID）
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("校验下游模块是否存在有效数据")
    @PostMapping("/checkNextModuleExistEffectiveData")
    public ResultObject checkNextModuleExistEffectiveData(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.checkNextModuleExistEffectiveData(params,userInfo);
    }


    /**
     * 获取订单表头流向各个模块的情况数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("获取订单表头流向各个模块的情况数据")
    @PostMapping("/checkOrderHeadIsNextModule")
    public ResultObject checkOrderHeadIsNextModule(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.checkOrderHeadIsNextModule(params,userInfo);
    }



    /**
     * 作废数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("作废数据")
    @PostMapping("/cancelData")
    public ResultObject cancelData(@RequestBody BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.cancelData(params,userInfo);
    }


    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIOrderHeadDto> list, UserInfoToken userInfoToken) {
        // 获取供应商列表信息
        List<Map<String,String>> supplierList = bizIOrderHeadService.getSupplierList(userInfoToken);
        for(BizIOrderHeadDto item : list) {
            // 转换业务类型
            if (StringUtils.isNotBlank(item.getBusinessType())){
                item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            }
            // 转换订单数据状态
            if (StringUtils.isNotBlank(item.getDataStatus())){
                item.setDataStatus(item.getDataStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getDataStatus()));
            }
            // 转换进货单据状态
            if (StringUtils.isNotBlank(item.getPurchaseDataStatus())){
                item.setPurchaseDataStatus(item.getPurchaseDataStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getPurchaseDataStatus()));
            }
            // 转换销售单据状态
            if (StringUtils.isNotBlank(item.getSalesDataStatus())){
                item.setSalesDataStatus(item.getSalesDataStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getSalesDataStatus()));
            }
            // 转换入库回单状态
            if (StringUtils.isNotBlank(item.getInboundReceiptStatus())){
                item.setInboundReceiptStatus(item.getInboundReceiptStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getInboundReceiptStatus()));
            }

            // 转换出库回单状态
            if (StringUtils.isNotBlank(item.getOutboundReceiptStatus())){
                item.setOutboundReceiptStatus(item.getOutboundReceiptStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getOutboundReceiptStatus()));
            }
            // 转换供应商信息
            if (StringUtils.isNotBlank(item.getPartyB())){
                item.setPartyB(getSupplierNameByCode(supplierList,item.getPartyB()));
            }

        }
    }

    /**
     * 根据供应商code 返回对应 供应商名称+供应商代码,如果没匹配到 返回原来的供应商代码
     * @param supplierList 供应商列表
     * @param supplierCode 供应商代码
     * @return 返回结果 {供应商名称+供应商代码}
     */
    public String getSupplierNameByCode(List<Map<String,String>> supplierList, String supplierCode) {
        if (CollectionUtils.isNotEmpty(supplierList) && StringUtils.isNotBlank(supplierCode)) {
            // 根据供应商code找到列表中code对应的map数据
            Map<String, String> supplierMap = supplierList.stream().filter(map -> supplierCode.equals(map.get("value"))).findFirst().orElse(null);
            if (supplierMap != null) {
                return supplierMap.get("value") + " " + supplierMap.get("label");
            }
        }
        return supplierCode;
    }

    @ApiOperation("打印接口")
    @PostMapping("print/{sid}/{sType}")
    public ResponseEntity print(@PathVariable String sid,@PathVariable String sType, UserInfoToken userInfo) throws Exception {
        BizIOrderHeadDto dto = bizIOrderHeadService.print(sid,userInfo);
        String templateName = "biz_i_order.xlsx";
        String outName = xdoi18n.XdoI18nUtil.t("订单打印") + dto.getOrderNo() + ".pdf";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        if ("excel".equals(sType)) {
            outName = xdoi18n.XdoI18nUtil.t("订单打印") + dto.getOrderNo() + ".xlsx";
//            fileName = UUID.randomUUID().toString() + ".xlsx";
        }
        String exportFileName = exportService.export(Arrays.asList(dto), dto.getOrderList(), fileName, templateName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if("pdf".equals(sType)) {
            fileBytes = ExportService.excelToPdf(fileBytes);
        }

//        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
//        outName = outName.replaceAll("\\+","%20");
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }


    /**
     * 订单模块-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("订单模块-获取供应商列表信息")
    @PostMapping("/getOrderSupplierList")
    public ResultObject getOrderSupplierList(BizIOrderHeadParam params, UserInfoToken userInfo) {
        return bizIOrderHeadService.getOrderSupplierList(params,userInfo);
    }
}
