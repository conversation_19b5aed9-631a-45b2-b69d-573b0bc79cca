package com.dcjet.cs.api.nonAuxiliaryMaterials;


import com.dcjet.cs.common.service.ExcelService;

import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;

import com.dcjet.cs.dto.dec.GenerateTBParams;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadExportParam;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.AttachmentRequestDto;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHeadList;
import com.dcjet.cs.nonAuxiliaryMaterials.service.BizNonIncomingGoodsHeadService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
* TBizNonIncomingGoodsHeadController层
*
* <AUTHOR>
* @date 2025-05-22 15:28:59
*/
@RestController
@RequestMapping("v1/bizNonIncomingGoodsHead")
@Api(tags = "进货管理-表头数据接口")
public class BizNonIncomingGoodsHeadController {

    @Resource
    private BizNonIncomingGoodsHeadService tBizNonIncomingGoodsHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进货管理-表头数据数据
     * @param tBizNonIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("list")
    public ResultObject<List<BizNonIncomingGoodsHeadDto>> getListPaged(@RequestBody BizNonIncomingGoodsHeadParam tBizNonIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsHeadDto>> paged = tBizNonIncomingGoodsHeadService.getListPaged(tBizNonIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param tBizNonIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizNonIncomingGoodsHeadDto> insert(@Valid @RequestBody BizNonIncomingGoodsHeadParam tBizNonIncomingGoodsHeadParam, UserInfoToken userInfo) {
        ResultObject<BizNonIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizNonIncomingGoodsHeadDto tBizNonIncomingGoodsHeadDto = tBizNonIncomingGoodsHeadService.insert(tBizNonIncomingGoodsHeadParam, userInfo);
        if (tBizNonIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizNonIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param id
     * @param tBizNonIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/{id}")
    public ResultObject<BizNonIncomingGoodsHeadDto> update(@PathVariable String id, @Valid @RequestBody BizNonIncomingGoodsHeadParam tBizNonIncomingGoodsHeadParam, UserInfoToken userInfo) {
        tBizNonIncomingGoodsHeadParam.setId(id);
        BizNonIncomingGoodsHeadDto tBizNonIncomingGoodsHeadDto = tBizNonIncomingGoodsHeadService.update(tBizNonIncomingGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (tBizNonIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizNonIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		tBizNonIncomingGoodsHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizNonIncomingGoodsHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizNonIncomingGoodsHeadList> BizNonIncomingGoodsHeadLists = tBizNonIncomingGoodsHeadService.selectAllNew(exportParam.getExportColumns(), userInfo);
        convertForPrint(BizNonIncomingGoodsHeadLists);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), BizNonIncomingGoodsHeadLists);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizNonIncomingGoodsHeadList> list) {
        for(BizNonIncomingGoodsHeadList item : list) {
            // 处理进货单据状态
            if (StringUtils.isNotBlank(item.getDataState())) {
                if ("0".equals(item.getDataState())) {
                    item.setDataState("0 编制");
                }else if ("1".equals(item.getDataState())) {
                    item.setDataState("1 确认");
                }else if ("2".equals(item.getDataState())) {
                    item.setDataState("2 作废");
                }
            }

            if (StringUtils.isNotBlank(item.getSellStatus())) {
                if ("0".equals(item.getSellStatus())) {
                    item.setSellStatus("0 编制");
                }else if ("1".equals(item.getSellStatus())) {
                    item.setSellStatus("1 确认");
                }else if ("2".equals(item.getSellStatus())) {
                    item.setSellStatus("2 作废");
                }else if ("-1".equals(item.getSellStatus())) {
                    item.setSellStatus("-1 未生成");
                }
            }

            // 处理供应商
            // 不处理

        }
    }



    /**
     * 进货管理-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getSupplierList(params,userInfo);
    }


    /**
     * 进货管理-获取港口列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getPortList")
    public ResultObject getPortList(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getPortList(params,userInfo);
    }



    /**
     * 进货管理-获取币制信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取币制信息")
    @PostMapping("/getCurrList")
    public ResultObject getCurrList(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getCurrList(params,userInfo);
    }



    /**
     * 进货管理-获取价格条款
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取价格条款")
    @PostMapping("/getPriceTermList")
    public ResultObject getPriceTermList(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getPriceTermList(params,userInfo);
    }


    /**
     * 进货管理-获取单位信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取单位信息")
    @PostMapping("/getUnitList")
    public ResultObject getUnitList(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getUnitList(params,userInfo);
    }


    /**
     * 确定 进货信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("确定进货信息")
    @PostMapping("/confirmIncomingGoods")
    public ResultObject<BizNonIncomingGoodsHeadDto> confirmIncomingGoods(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.confirmIncomingGoods(params,userInfo);
    }


    @ApiOperation("获取可提取的合同信息")
    @PostMapping("/getExtractContractInfo")
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getExtractContractInfo(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getExtractContractInfo(params,userInfo);
    }




    @ApiOperation("根据表头ID获取表头信息")
    @PostMapping("/getHeadInfoById")
    public ResultObject<BizNonIncomingGoodsHeadDto> getHeadInfoById(@RequestBody BizNonIncomingGoodsHeadParam params,UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getHeadInfoById(params,userInfo);
    }



    /**
     * 分页获取进货管理-表头数据数据
     * @param tBizNonIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("listNew")
    public ResultObject<List<BizNonIncomingGoodsHeadList>> getListPagedNew(@RequestBody BizNonIncomingGoodsHeadParam tBizNonIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsHeadList>> paged = tBizNonIncomingGoodsHeadService.getListPagedNew(tBizNonIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("获取进货信息供应商去重汇总")
    @PostMapping("/getSupplierListDistinct")
    public ResultObject<List<Map<String,String>>> getSupplierListDistinct(@RequestBody BizIAuxmatForContractHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.getSupplierListDistinct(params,userInfo);
    }



    @ApiOperation("作废数据")
    @PostMapping("/cancel")
    public ResultObject cancel(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.cancel(params,userInfo);
    }
    @ApiOperation("发送报关")
    @PostMapping("/sendEntry")
    public ResultObject sendEntry(@RequestBody BizNonIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.sendEntry(params,userInfo);
    }

    @ApiOperation("提取合同")
    @PostMapping("/extractContract")
    public ResultObject extractContract(@RequestBody BizNonIncomingGoodsHeadParam params,UserInfoToken userInfo) {
        return tBizNonIncomingGoodsHeadService.extractContract(params,userInfo);
    }

    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("listToCustomerAccount")
    public ResultObject<List<BizNonIncomingGoodsHeadDto>> getListPagedToCustomerAccount(@RequestBody BizNonIncomingGoodsHeadParam tBizNonIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsHeadDto>> paged = tBizNonIncomingGoodsHeadService.getListPagedToCustomerAccount(tBizNonIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

}
