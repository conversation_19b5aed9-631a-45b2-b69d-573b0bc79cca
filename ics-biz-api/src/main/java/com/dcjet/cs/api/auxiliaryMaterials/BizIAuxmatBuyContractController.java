package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractExportParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractWithDetailParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractWithDetailDto;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatBuyContractService;
import com.dcjet.cs.dto.dec.BizIOrderHeadDto;
import com.dcjet.cs.dto.dec.BizIOrderHeadExportParam;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.lang3.StringUtils;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@RestController
@RequestMapping("v1/bizIAuxmatBuyContract")
@Api(tags = "接口")
public class BizIAuxmatBuyContractController extends BaseController {
    @Resource
    private BizIAuxmatBuyContractService bizIAuxmatBuyContractService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIAuxmatBuyContractParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIAuxmatBuyContractDto>> getListPaged(@RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIAuxmatBuyContractDto>> paged = bizIAuxmatBuyContractService.getListPaged(bizIAuxmatBuyContractParam, pageParam);
        return paged;
    }
    /**
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIAuxmatBuyContractDto> insert(@Valid @RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatBuyContractDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIAuxmatBuyContractDto bizIAuxmatBuyContractDto = bizIAuxmatBuyContractService.insert(bizIAuxmatBuyContractParam, userInfo);
        if (bizIAuxmatBuyContractDto != null) {
            resultObject.setData(bizIAuxmatBuyContractDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIAuxmatBuyContractDto> update(@PathVariable String sid, @Valid @RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        bizIAuxmatBuyContractParam.setId(sid);
        BizIAuxmatBuyContractDto bizIAuxmatBuyContractDto = bizIAuxmatBuyContractService.update(bizIAuxmatBuyContractParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIAuxmatBuyContractDto != null) {
            resultObject.setData(bizIAuxmatBuyContractDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIAuxmatBuyContractService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIAuxmatBuyContractExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIAuxmatBuyContractDto> bizIAuxmatBuyContractDtos = bizIAuxmatBuyContractService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIAuxmatBuyContractDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIAuxmatBuyContractDtos);
    }

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIAuxmatBuyContractDto> list, UserInfoToken userInfoToken){
        for (BizIAuxmatBuyContractDto item : list){
            // 转换业务类型
            if (StringUtils.isNotBlank(item.getBusinessType())){
                item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            }
            // 转换订单数据状态
            if (StringUtils.isNotBlank(item.getStatus())){
                item.setStatus(item.getStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getStatus()));
            }
            //转换审核状态
            if (StringUtils.isNotBlank(item.getApprStatus())) {
                item.setApprStatus(CommonEnum.OrderApprStatusEnum.getValue(item.getApprStatus()));
            }
            //转换业务区分
            if (StringUtils.isNotBlank(item.getBusinessDistinction())) {
                item.setBusinessDistinction(CommonEnum.BusinessDistinctionEnum.getValue(item.getBusinessDistinction()));
            }
        }
    }

    /**
     * 确认数据状态接口
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm/{sid}")
    public ResultObject confirmStatus(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.confirmStatus(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.sendApproval(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.invalidate(sid, userInfo);
    }

    /**
     * 校验是否存在 同一个合同号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("校验是否存在 同一个合同号是否存在未作废的数据")
    @PostMapping("/checkContractIdNotCancel")
    public ResultObject checkContractIdNotCancel(@RequestBody BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.checkContractIdNotCancel(params, userInfo);
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.copyVersion(params, userInfo);
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @param userInfo 用户信息
     * @return 返回校验结果
     */
    @ApiOperation("校验单行表体数据")
    @PostMapping("/validateDetailData")
    public ResultObject<String> validateDetailData(@Valid @RequestBody BizIAuxmatBuyContractListParam detail, UserInfoToken userInfo) {
        try {
            String errorMessage = bizIAuxmatBuyContractService.validateSingleDetailData(detail);

            if (StringUtils.isBlank(errorMessage)) {
                return ResultObject.createInstance(true, "校验通过", "");
            } else {
                return ResultObject.createInstance(false, errorMessage);
            }
        } catch (Exception e) {
            return ResultObject.createInstance(false, "校验异常: " + e.getMessage(), "");
        }
    }

    /**
     * 新增表头和表体数据
     * @param bizIAuxmatBuyContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("新增表头和表体数据")
    @PostMapping("/insertBuyContractWithDetails")
    public ResultObject<BizIAuxmatBuyContractWithDetailDto> insertBuyContractWithDetails(@Valid @RequestBody BizIAuxmatBuyContractWithDetailParam bizIAuxmatBuyContractWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatBuyContractWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        try {
            BizIAuxmatBuyContractWithDetailDto bizIAuxmatBuyContractWithDetailDto = bizIAuxmatBuyContractService.insertBuyContractWithDetails(bizIAuxmatBuyContractWithDetailParam, userInfo);
            if (bizIAuxmatBuyContractWithDetailDto != null) {
                resultObject.setData(bizIAuxmatBuyContractWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.INSERT_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }

    /**
     * 修改表头和表体数据
     * @param bizIAuxmatBuyContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("修改表头和表体数据")
    @PostMapping("/updateBuyContractWithDetails")
    public ResultObject<BizIAuxmatBuyContractWithDetailDto> updateBuyContractWithDetails(@Valid @RequestBody BizIAuxmatBuyContractWithDetailParam bizIAuxmatBuyContractWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatBuyContractWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        try {
            BizIAuxmatBuyContractWithDetailDto bizIAuxmatBuyContractWithDetailDto = bizIAuxmatBuyContractService.updateBuyContractWithDetails(bizIAuxmatBuyContractWithDetailParam, userInfo);
            if (bizIAuxmatBuyContractWithDetailDto != null) {
                resultObject.setData(bizIAuxmatBuyContractWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.UPDATE_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }

/*    *//**
     * 获取划款通知相关信息接口
     * @param sid
     * @param userInfo
     * @return
     *//*
    @ApiOperation("获取划款通知相关信息接口")
    @PostMapping("getTransferNoticeMesBefore/{sid}")
    public ResultObject getTransferNoticeMesBefore(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.confirmStatus(sid, userInfo);
    }*/


    /**
     * 划款通知
     * @param bizIAuxmatBuyContract
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知")
    @PostMapping("handlerTransferNotice")
    public ResultObject handlerTransferNotice(@RequestBody BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.handlerTransferNotice(bizIAuxmatBuyContract, userInfo);
    }

    /**
     * 划款通知保存
     * @param bizIAuxmatBuyContract
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知保存")
    @PostMapping("saveTransferNotice")
    public ResultObject saveTransferNotice(@RequestBody BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo) {
        return bizIAuxmatBuyContractService.saveTransferNotice(bizIAuxmatBuyContract, userInfo);
    }
    /**
     * 划款通知保存校验
     * @param bizIAuxmatBuyContract
     * @param
     * @return
     */
    @ApiOperation("划款通知保存校验")
    @PostMapping("checkTransferNotice")
    public ResultObject checkTransferNotice(@RequestBody BizIAuxmatBuyContract bizIAuxmatBuyContract) {
        return bizIAuxmatBuyContractService.checkTransferNotice(bizIAuxmatBuyContract);
    }
    /**
     * 划款通知保存
     * @param bizIAuxmatBuyContract
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知保存打印")
    @PostMapping("saveTransferNoticePrint")
    public ResponseEntity saveTransferNoticePrint(@RequestBody BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo) throws Exception {
        return bizIAuxmatBuyContractService.saveTransferNoticePrint(bizIAuxmatBuyContract, userInfo);
    }
}
