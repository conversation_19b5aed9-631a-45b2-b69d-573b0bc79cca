--liquibase formatted sql

--changeset xbxu1:
CREATE TABLE IF NOT EXISTS  "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_NO" VARCHAR(120),
    "CURR" VARCHAR(50),
    "CUSTOMER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "INVOICE_NO" VARCHAR(120),
    "PORT_OF_DEPARTURE" VARCHAR(100),
    "DESTINATION" VARCHAR(100),
    "PRICE_TERM" VARCHAR(40),
    "PRICE_TERM_PORT" VARCHAR(100),
    "VESSEL_VOYAGE" VARCHAR(200),
    "SAILING_DATE" TIMESTAMP(6),
    "SALES_DATE" TIMESTAMP(6),
    "DOCUMENT_CREATOR" VARCHAR(20),
    "DOCUMENT_DATE" TIMESTAMP(6),
    "DOCUMENT_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    "DATE_OF_CONTRACT" TIMESTAMP(6),
    "IS_NEXT" VARCHAR(1) DEFAULT '0',
    "PURCHASE_CONTRACT_NO" VARCHAR(120),
    "ENTRY_NO" VARCHAR(400),
    "ENTRY_DATE" TIMESTAMP(6),
    "SEND_ENTRY" VARCHAR(20),
    "NOTE" VARCHAR(400),
    "NON_INCOMING_INVOICE_NO" VARCHAR(60),
    "HEAD_ID" VARCHAR(60),
    CONSTRAINT "PK_T_BIZ_NON_INCOMING_GOODS_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD" IS '非国营贸易辅料-进口管理';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."ID" IS '主键ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."DATA_STATE" IS '数据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."TRADE_CODE" IS '企业10位编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."SYS_ORG_CODE" IS '组织机构代码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."PARENT_ID" IS '父级ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_USER_NAME" IS '更新用户名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND10" IS '扩展字段10';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CONTRACT_NO" IS '合同号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."PURCHASE_NO" IS '进货单号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CURR" IS '币种';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CUSTOMER" IS '客户';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."SUPPLIER" IS '供应商';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."INVOICE_NO" IS '发票号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."PORT_OF_DEPARTURE" IS '启运港';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."DESTINATION" IS '目的地/港';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."PRICE_TERM" IS '价格条款';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."VESSEL_VOYAGE" IS '船名航次';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."SAILING_DATE" IS '开航日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."SALES_DATE" IS '做销日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_CREATOR" IS '制单人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_DATE" IS '制单日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_STATUS" IS '单据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."CONFIRM_TIME" IS '确认时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."APPROVAL_STATUS" IS '审批状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."IS_NEXT" IS '是否流入下一个节点';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."ENTRY_NO" IS '报关单号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."ENTRY_DATE" IS '报关日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."SEND_ENTRY" IS '发送报关';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."NOTE" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_HEAD"."NON_INCOMING_INVOICE_NO" IS '发票号码';


CREATE TABLE IF NOT EXISTS  "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "GOODS_NAME" VARCHAR(160),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" NUMERIC(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "DELIVERY_DATE" TIMESTAMP(6),
    "TOTAL_USD" NUMERIC(19,4),
    "REMARKS" VARCHAR(400),
    "HEAD_ID" VARCHAR(40),
    "IN_QUANTITY" NUMERIC(19,6),
    "IN_UNIT" VARCHAR(60),
    "CURR" VARCHAR(40),
    "INVOICE_NO" VARCHAR(60),
    "CONTRACT_LIST_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZT_BIZ_NON_INCOMING_GOODS_LIST_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST" IS '非国营贸易辅料-进口管理-表体列表';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."ID" IS '主键ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."DATA_STATE" IS '数据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."TRADE_CODE" IS '企业10位编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."SYS_ORG_CODE" IS '组织机构代码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."PARENT_ID" IS '父级ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_USER_NAME" IS '更新用户名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND10" IS '扩展字段10';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."GOODS_NAME" IS '商品名称';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."PRODUCT_MODEL" IS '商品描述';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."QUANTITY" IS '数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."UNIT" IS '单位';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."UNIT_PRICE" IS '单价';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."AMOUNT" IS '金额';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."DELIVERY_DATE" IS '交货日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."TOTAL_USD" IS '总价折美元';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."REMARKS" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."HEAD_ID" IS '表头HEAD_ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."IN_QUANTITY" IS '进口数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."IN_UNIT" IS '进口单位';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."CURR" IS '币种';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."INVOICE_NO" IS '进口发票号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_NON_INCOMING_GOODS_LIST"."CONTRACT_LIST_ID" IS '合同表体的ID';



--changeset fhfang:1
-- alter table BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD add column NON_INCOMING_Invoice_No varchar (60);
-- comment on column BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD.NON_INCOMING_Invoice_No is '发票号码';
-- alter table BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD add column head_id varchar (60);

--changeset cblin:1
CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    BUSINESS_TYPE VARCHAR(120) NULL,
    ACCOUNT_NO VARCHAR(120) NULL,
    PURCHASE_ORDER_NO VARCHAR(120) NULL,
    CONTRACT_NO VARCHAR(120) NULL,
    CUSTOMER VARCHAR(400) NULL,
    CURR VARCHAR(20) NULL,
    EXCHANGE_RATE NUMERIC(19,6) NULL,
    GOODS_PRICE NUMERIC(19,5) NULL,
    AGENT_FEE_RATE NUMERIC(19,6) NULL,
    AGENT_FEE NUMERIC(19,2) NULL,
    AGENT_TAX_FEE NUMERIC(19,2) NULL,
    AGENT_FEE_TOTAL NUMERIC(19,2) NULL,
    BUSINESS_DATE TIMESTAMP NULL,
    G_NAME VARCHAR(160) NULL,
    SEND_FINANCE VARCHAR(20) NULL,
    PRODUCR_SOME VARCHAR(600) NULL,
    NOTE VARCHAR(400) NULL,
    RED_FLUSH VARCHAR(20) NULL,
    STATUS VARCHAR(20) NULL,
    APPR_STATUS VARCHAR(20) NULL,
    CONFIRM_TIME TIMESTAMP NULL,
    IS_CONFIRM VARCHAR(10) NULL,
    PURCHASE_MARK VARCHAR(20) NULL,
    PURCHASE_NO_MARK VARCHAR(600) NULL,
    VERSION_NO VARCHAR(100) NULL,
    CONSTRAINT T_BIZ_CUSTOMER_ACCOUNT_PK PRIMARY KEY (SID)
    );


COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.BUSINESS_TYPE IS '业务类型';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.ACCOUNT_NO IS '结算单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_ORDER_NO  IS '进货单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CONTRACT_NO IS '合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CUSTOMER IS '客户';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CURR IS '币种';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXCHANGE_RATE IS '汇率';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.GOODS_PRICE IS '货款';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE_RATE IS '代理费率%';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE IS '代理费（不含税金额）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_TAX_FEE IS '代理费税额';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE_TOTAL IS '代理费（价税合计）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.BUSINESS_DATE IS '业务日期';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.G_NAME IS '商品名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SEND_FINANCE IS '发送财务系统';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PRODUCR_SOME IS '商品与数量';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.NOTE IS '备注';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.RED_FLUSH IS '是否红冲';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.STATUS IS '单据状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.APPR_STATUS IS '审核状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CONFIRM_TIME IS '确认时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.IS_CONFIRM IS '是否确认';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_MARK IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_NO_MARK IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.VERSION_NO IS '版本号';


alter table BIZ_TOBACOO.T_BIZ_STORE_E_HEAD add if not exists red_flush varchar(10);
comment on column BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.red_flush is '是否红冲';

